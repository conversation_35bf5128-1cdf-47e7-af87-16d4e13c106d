/*
 * @Author: 47844 <EMAIL>
 * @Date: 2025-07-15 14:17:05
 * @LastEditors: 47844 <EMAIL>
 * @LastEditTime: 2025-08-20 16:46:37
 * @FilePath: \institute-front\packages\key-hmes-front\src\routes\workshop\InspectionMonitorBoard\index.module.less
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

.datePickerWrapper {
  width: 180px;
  height: 30px;
  position: relative;
}

.pieChartBackground {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 85% !important; // 强制设置为85%
  height: 85% !important; // 强制设置为85%
  transform: translate(-50%, -50%);
  background-image: url('../../assets/FinishedProductInspectionDashboardAss/pie_bac.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.boardContainer {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  overflow: hidden;
  max-width: 100vw;
  background-image: url('../../assets/FinishedProductInspectionDashboardAss/background.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: top center;
  font-size: 10px; // 设置更小的基础字体大小

  :global(.custom-date-picker) {
    .c7n-pro-calendar-picker-wrapper {
      background: transparent !important;
      border: none !important;
      height: 100% !important;
      width: 100% !important;
    }

    .c7n-pro-calendar-cell-inner {
      width: 100% !important;
      color: #fff !important;
      height: 100% !important;
      font-size: 10px;
    }

    .c7n-pro-calendar-picker {
      background: transparent !important;
      border: none !important;
      height: 100% !important;
      width: 100% !important;
    }
  }

  :global(.filter-modal .c7n-pro-modal-content) {
    background: transparent !important;
    box-shadow: none !important;
  }
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
  height: 74px;
  padding: 2px 30px 28px 30px;
  box-sizing: border-box;
  position: relative;
  background-image: url('../../assets/FinishedProductInspectionDashboardAss/header_bac.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
}

.headerLeft,
.headerRight {
  display: flex;
  align-items: center;
  gap: 4px;
  flex: 1;
}

.headerRight {
  justify-content: flex-end;
  margin-right: 4%;
}

.fullscreenButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: transparent;
  border: none;
  color: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-left: 10px;

  &:hover {
    color: #4a90e2;
    transform: scale(1.1);
  }

  &:active {
    transform: scale(0.9);
  }

  :global(.icon) {
    font-size: 16px;
  }
}

.datePickerWrapper {
  width: 144px;
  height: 30px;
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
  background-image: url('../../assets/FinishedProductInspectionDashboardAss/time_bac.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;

  :global {
    .c7n-pro-calendar-picker-wrapper {
      background: transparent !important;
      border: none !important;
      height: 100% !important;
      width: 86% !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      box-shadow: none !important;
      outline: none !important;
      position: relative !important;
    }

    .c7n-pro-calendar-picker {
      background-color: transparent !important;
      border: none !important;
      height: 100% !important;
      width: 100% !important;
      color: #e2ffff !important;
      font-size: 12px !important;
      padding-left: 28px !important;
      display: flex !important;
      align-items: center !important;
      line-height: 30px !important;

      input {
        background: transparent !important;
        border: none !important;
        display: flex !important;
        align-items: center !important;
        color: #e2ffff !important;
        font-size: 14px !important;
        width: 100% !important;
        height: 100% !important;
        text-align: left !important;
        box-shadow: none !important;
        outline: none !important;
        line-height: 30px !important;

        &::placeholder {
          color: #e2ffff !important;
          opacity: 1 !important;
          font-size: 14px !important;
        }

        &:focus {
          background: transparent !important;
          border: none !important;
          box-shadow: none !important;
          outline: none !important;
          color: #b9d4ff !important;
        }
      }
    }

    .c7n-pro-custom-date-picker {
      font-size: 14px !important;
      background: transparent !important;
      height: 30px;
      width: 140px;
      border: 0;
      padding-left: 40px;

      input {
        background: transparent !important;
        border: none !important;
        display: flex !important;
        align-items: center !important;
        color: #b9d4ff !important;
        font-size: 14px !important;
        width: 100% !important;
        height: 100% !important;
        text-align: left !important;
        box-shadow: none !important;
        outline: none !important;
        line-height: 30px !important;

        &::placeholder {
          color: #b9d4ff !important;
          opacity: 1 !important;
          font-size: 14px !important;
        }

        &:focus {
          background: transparent !important;
          border: none !important;
          box-shadow: none !important;
          outline: none !important;
          color: #b9d4ff !important;
        }
      }
    }
    .c7n-pro-calendar-picker-inner {
      background: transparent !important;
      width: 100% !important;
      height: 100% !important;
    }

    .c7n-pro-calendar-picker-suffix {
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      padding: 10px;
      .icon {
        color: #b9d4ff !important;
        font-size: 14px !important;
      }
    }

    .icon-date_range {
      color: #c3e1e9 !important;
      font-size: 14px !important;
    }
  }
}

.dateLabel {
  font-size: 12px;
  color: #b9d4ff;
}

.titleGroup {
  flex: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.title {
  background-image: url('../../assets/FinishedProductInspectionDashboardAss/header_title.png');
  background-position: center center;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  height: 24px;
  width: 330px;
}

.enTitle {
  font-size: 10px;
  letter-spacing: 2px;
  color: #fff;
  opacity: 0.7;
  padding-top: 4px;
}

.headerCenter {
  position: absolute;
  top: 36px;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  z-index: 10;
}

.timeWrapper {
  display: flex;
  flex-direction: row;
  align-items: end;
  gap: 15px;
  background-position: left center;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  padding: 10px 0;
}
.time {
  font-size: 16px;
  color: #fff;
}
.date {
  font-size: 12px;
  color: #fff;
}
.week {
  font-size: 12px;
  color: #fff;
}

.mainContent {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
  min-height: 0;
  box-sizing: border-box;
  margin-top: 14px;
  padding: 0 20px 20px;
  max-width: 100%;
  overflow-x: hidden;
}

.topRow {
  display: flex;
  height: 58%;
  min-height: 0;
  gap: 32px;
  > .panel {
    flex: 1;
    min-width: 0;
    min-height: 0;
    overflow: hidden;
  }
}
.bottomRow {
  height: 42%;
  min-height: 0;
  max-width: 100%;
  overflow: hidden;
}

.panel {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-sizing: border-box;
  background: transparent;
  padding-top: 0;
}

.panelHeader {
  display: flex;
  align-items: center;
  height: 34px;
  justify-content: space-between;
  margin-bottom: 4px;
  flex-shrink: 0;
  padding: 0 0 6px 26px;
}

.panelHeader {
  background-image: url('../../assets/FinishedProductInspectionDashboardAss/group_bac.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.panelTitle {
  font-size: 13px;
  color: #fff;
  padding: 15px 0;
  flex-shrink: 0;
  position: relative;
  display: flex;
  align-items: center;
  .title {
    background-image: url('../../assets/FinishedProductInspectionDashboardAss/header_title.png');
    background-size: 100% 100%;
    background-position: center;

    height: 40px;
    width: 40px;
  }
}

.panelExtra {
  display: flex;
  gap: 10px;
  margin-top: 7px;
}

.exportBtn {
  background-image: url('../../assets/firstArticleInspectionDashboardAss/button_bac.png') !important;
  background-size: 100% 100% !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  background-color: transparent !important;
  border: none !important;
  color: #ffffff !important;
  padding: 0 16px !important;
  cursor: pointer;
  font-size: 12px !important;
  line-height: 32px !important;
  height: 22px !important;
  min-width: 62px;
  max-width: 93px;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  box-shadow: none !important;
  border-radius: 0 !important;
  outline: none !important;
  transition: all 0.2s ease !important;

  // 覆盖猪齿鱼按钮的默认样式
  &.c7n-pro-btn {
    background-image: url('../../assets/firstArticleInspectionDashboardAss/button_bac.png') !important;
    background-size: 100% 100% !important;
    background-repeat: no-repeat !important;
    background-position: center !important;
    background-color: transparent !important;
    border: none !important;
    color: #ffffff !important;
    box-shadow: none !important;
    border-radius: 0 !important;
  }

  &.c7n-pro-btn-raised {
    box-shadow: none !important;
  }

  &.c7n-pro-btn-default {
    background-image: url('../../assets/firstArticleInspectionDashboardAss/button_bac.png') !important;
    background-size: 100% 100% !important;
    background-repeat: no-repeat !important;
    background-position: center !important;
    background-color: transparent !important;
    border: none !important;
    color: #ffffff !important;
  }

  &:hover {
    transform: scale(1.03) !important;
    filter: brightness(1.15) !important;
    background-image: url('../../assets/firstArticleInspectionDashboardAss/button_bac.png') !important;
    background-size: 100% 100% !important;
    background-repeat: no-repeat !important;
    background-position: center !important;
    background-color: transparent !important;
    border: none !important;
    color: #ffffff !important;
    box-shadow: none !important;
  }

  &:active {
    transform: scale(0.98) !important;
    background-image: url('../../assets/firstArticleInspectionDashboardAss/button_bac.png') !important;
    background-size: 100% 100% !important;
    background-repeat: no-repeat !important;
    background-position: center !important;
    background-color: transparent !important;
    border: none !important;
    color: #ffffff !important;
  }

  &:focus {
    background-image: url('../../assets/firstArticleInspectionDashboardAss/button_bac.png') !important;
    background-size: 100% 100% !important;
    background-repeat: no-repeat !important;
    background-position: center !important;
    background-color: transparent !important;
    border: none !important;
    color: #ffffff !important;
    box-shadow: none !important;
    outline: none !important;
  }
}

.assetButton {
  background-image: url('../../assets/FinishedProductInspectionDashboardAss/button_bac.png');
  background-size: 100% 100%;
  background-position: center;
  background-color: transparent;
  border: none;
  color: #c4d1e2;
  padding: 3px 10px;
  cursor: pointer;
  font-size: 11px;
  width: 76px;
  height: 26px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.filterButton {
  background-image: url('../../assets/FinishedProductInspectionDashboardAss/button_bac.png');
  background-size: 100% 100%;
  background-position: center;
  background-color: transparent;
  border: none;
  color: #fff;
  padding: 6px 16px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
  white-space: nowrap;
  min-width: 60px;
  height: 32px;

  &:hover {
    transform: scale(1.05);
    filter: brightness(1.2);
  }

  &:active {
    transform: scale(0.98);
  }

  &:last-child {
    margin-right: 0;
  }
}

.panelBody {
  flex-grow: 1;
  overflow: auto;
  display: flex;
  flex-direction: column;
  max-width: 100%;
  box-sizing: border-box;
}

.chartPanelBody {
  padding: 0;
  position: relative;
  overflow: hidden;
  height: calc(100% - 60px); // 增加减去的高度，防止溢出
  max-height: calc(45vh - 60px); // 确保不超过容器最大高度
  
  > div {
    width: 100% !important;
    height: 100% !important;
    max-height: 100% !important;
  }
}

.customTable {
  height: 100%;
  display: flex;
  flex-direction: column;
  color: #fff;
  overflow: hidden;
}

.tableHeader,
.tableRow {
  display: grid;
  grid-template-columns: 1fr 1.5fr 1fr 1.5fr 1fr;
  text-align: center;
  padding: 8px 2px;
  align-items: center;
  gap: 20px;
}
.tableHeader2,
.tableRow2 {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr 1fr 1fr;
  text-align: center;
  padding: 8px !important;
  align-items: center;
  gap: 20px;
}

.tableHeader,
.tableHeader2 {
  background-image: url('../../assets/FinishedProductInspectionDashboardAss/table_title.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  background-color: transparent;
  font-weight: bold;
  color: #5a9df4;
  font-size: 10px;
  text-align: start;
  > span {
    white-space: nowrap;
    text-align: start;
    padding: 0 2px;
  }
}

.tableHeader,
.tableRow {
  padding: 8px !important;
}

.tableBody,
.materialFilterModalBody .materialFilterListBody {
  &::-webkit-scrollbar {
    width: 12px !important;
  }
  &::-webkit-scrollbar-track {
    background-color: #0b1a3e !important;
    border: 2px solid #6274a4 !important;
    margin: 0 !important;
    border-radius: 0 !important;
  }
  &::-webkit-scrollbar-thumb {
    background-color: #869ecd !important;
    border-radius: 5px !important;
    border: 3px solid transparent !important;
    background-clip: content-box !important;
  }
}

.tableBody {
  flex-grow: 1;
  overflow-y: auto;
  color: #ffffff;
  font-size: 1.2em;
  scroll-behavior: smooth;
  max-height: calc(100% - 40px);
}

.tableRow,
.tableRow2 {
  border-bottom: 1px solid #16366b;
  transition: all 0.3s ease;
  opacity: 1;
  transform: translateY(0);
  color: #ffffff;
  font-size: 9px;

  &:nth-child(even) {
    background-color: rgba(44, 58, 135, 0.67) !important;
  }
  &:nth-child(odd) {
    background-color: transparent !important;
  }

  &:hover {
    background-color: rgba(26, 58, 139, 0.2);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(74, 144, 226, 0.1);
  }

  &.entering {
    opacity: 0;
    transform: translateY(20px);
    animation: rowFadeIn 0.5s ease forwards;
  }
}

.clickableLink {
  color: #b4c2f1;
  cursor: pointer;
  text-decoration: underline;
  transition: color 0.3s ease;

  &:hover {
    color: #00d4a3;
    text-decoration: underline;
  }
}
// .activeRow {
//   background-color: rgba(0, 132, 255, 0.3) !important;
// }
.tableCell {
  white-space: nowrap;
  overflow: hidden;
  position: relative;
  max-width: 100%;
  min-width: 0;
  text-align: start;
}

@keyframes scrollText {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(var(--scroll-amount, -100px));
  }
}

.scrollingText {
  display: inline-block;
  animation: scrollText var(--scroll-duration, 10s) linear;
  white-space: nowrap;
}

.loadingRow {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50px;
  color: #b9d4ff;
  font-size: 12px;
  opacity: 0.8;
  background: linear-gradient(90deg, transparent, rgba(74, 144, 226, 0.1), transparent);
  animation: loadingShimmer 2s ease-in-out infinite;

  span {
    animation: loadingPulse 1.5s ease-in-out infinite;
    position: relative;

    &::after {
      content: '...';
      animation: loadingDots 1.5s steps(4, end) infinite;
    }
  }
}

@keyframes rowFadeIn {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes loadingPulse {
  0%,
  100% {
    opacity: 0.5;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}

@keyframes loadingShimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: 200px 0;
  }
}

@keyframes loadingDots {
  0%,
  20% {
    content: '';
  }
  40% {
    content: '.';
  }
  60% {
    content: '..';
  }
  80%,
  100% {
    content: '...';
  }
}
.tableIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  background-image: url('../../assets/FinishedProductInspectionDashboardAss/bottom_content_num.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  width: 36px;
  height: 15px;
  margin: 0px 4px;
}

.materialFilterList {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.materialFilterControls {
  padding: 12px 10px;
  border-bottom: 1px solid rgba(74, 144, 226, 0.3);
  flex-shrink: 0;
}

.filterRow {
  display: flex;
  align-items: center;

  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.filterItem {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 220px;
}

.filterLabel {
  color: #c3e1e9;
  font-size: 14px;
  white-space: nowrap;
  min-width: 80px;
}

.filterInput {
  flex: 1;
  padding: 6px 12px;
  background-color: rgba(11, 26, 62, 0.8);
  border: 1px solid rgba(74, 144, 226, 0.5);
  border-radius: 4px;
  color: #fff;
  font-size: 14px;
  min-width: 120px;

  &::placeholder {
    color: rgba(195, 225, 233, 0.6);
  }

  &:focus {
    outline: none;
    border-color: #4a90e2;
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
  }
}

.queryButton {
  padding: 6px 16px;
  /* background-image: 现在通过JSX style属性设置 */
  border: none;
  color: #fff;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
  white-space: nowrap;
  min-width: 60px;
  flex-shrink: 0;
  margin-right: 10px;
  height: 32px;

  &:hover {
    transform: scale(1.05);
    filter: brightness(1.1);
  }

  &:active {
    transform: scale(0.98);
  }
}

.clearButton {
  padding: 6px 16px;
  /* background-image: 现在通过JSX style属性设置 */
  border: none;
  color: #fff;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
  white-space: nowrap;
  min-width: 60px;
  flex-shrink: 0;
  height: 32px;

  &:hover {
    transform: scale(1.05);
    filter: brightness(1.1);
  }

  &:active {
    transform: scale(0.98);
  }
}

.materialFilterListHeader {
  display: grid;
  grid-template-columns: 1fr 1fr;
  padding: 12px 30px;
  background-image: url('../../assets/FinishedProductInspectionDashboardAss/table_title.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  background-color: transparent;
  font-weight: bold;
  color: #5a9df4;
  font-size: 10px;
  text-align: left;
  padding: 12px 30px;
}

.materialFilterListBody {
  flex-grow: 1;
  overflow-y: auto;
}

.materialFilterListRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  padding: 15px 30px;
  text-align: left;
  cursor: pointer;
  border-bottom: 1px solid rgba(74, 144, 226, 0.2);
  font-size: 12px;
  color: #ffffff;

  &:nth-child(even) {
    background-color: rgba(0, 132, 255, 0.1);
  }

  &:hover {
    background-color: rgba(74, 144, 226, 0.3);
  }

  &.selected {
    background-color: #4a90e2;
    color: #fff;
  }

  &:last-child {
    border-bottom: none;
  }
}

// 无数据提示样式
.noDataRow {
  padding: 40px 30px;
  text-align: center;
  color: rgba(195, 225, 233, 0.6);
  font-size: 14px;
  border-bottom: 1px solid rgba(74, 144, 226, 0.2);
}

.materialFilterModalFooter {
  padding: 25px 30px 20px;
  text-align: right;
  flex-shrink: 0;
  display: flex;
  justify-content: flex-end;
  gap: 15px;
}

.filterButtonBox {
  display: flex;
  gap: 15px;
}

.modalButton {
  /* background-image: 现在通过JSX style属性设置 */
  border: none;
  color: #fff;
  padding: 10px 24px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  height: 40px;
  min-width: 90px;
  transition: all 0.2s ease;

  &:hover {
    transform: scale(1.05);
    filter: brightness(1.2);
  }

  &:active {
    transform: scale(0.98);
  }

  &.confirmButton {
    filter: brightness(1.3);
  }

  &.cancelButton {
    filter: brightness(0.8);
  }
}

.modalOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  background-image: url('../../assets/FinishedProductInspectionDashboardAss/modal.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
}

.materialFilterModalContainer {
  background-color: transparent;
  border: none;
  width: 48%;
  height: 60%;
  display: flex;
  flex-direction: column;
  position: relative;
  margin-top: 5%;
  font-size: 12px;
}

.materialFilterModalTitle {
  padding: 8px 30px 15px;
  text-align: center;
  flex-shrink: 0;
  position: relative;
  font-size: 18px;
  color: #fff;
  z-index: 10;
}

.titleDecorator {
  color: #4a90e2;
  font-size: 14px;
  margin: 0 10px;
}

.materialFilterModalBody {
  display: flex;
  flex-direction: column;
  padding: 0 30px 0 30px;
  height: 380px;
  overflow: hidden;
}

.materialFilterModalFooter {
  padding: 25px 30px 20px;
  text-align: right;
  flex-shrink: 0;
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin-top: auto;
}

.exportBtn {
  background-image: url('../../assets/IncomingInspectionDashboard/button_bac.png') !important;
  background-size: 100% 100% !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  background-color: transparent !important;
  border: none !important;
  color: #ffffff !important;
  padding: 0 16px !important;
  cursor: pointer;
  font-size: 12px !important;
  line-height: 32px !important;
  height: 22px !important;
  min-width: 62px;
  max-width: 93px;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  box-shadow: none !important;
  border-radius: 0 !important;
  outline: none !important;
  transition: all 0.2s ease !important;

  // 覆盖猪齿鱼按钮的默认样式
  &.c7n-pro-btn {
    background-image: url('../../assets/IncomingInspectionDashboard/button_bac.png') !important;
    background-size: 100% 100% !important;
    background-repeat: no-repeat !important;
    background-position: center !important;
    background-color: transparent !important;
    border: none !important;
    color: #ffffff !important;
    box-shadow: none !important;
    border-radius: 0 !important;
  }

  &.c7n-pro-btn-raised {
    box-shadow: none !important;
  }

  &.c7n-pro-btn-default {
    background-image: url('../../assets/IncomingInspectionDashboard/button_bac.png') !important;
    background-size: 100% 100% !important;
    background-repeat: no-repeat !important;
    background-position: center !important;
    background-color: transparent !important;
    border: none !important;
    color: #ffffff !important;
  }

  &:hover {
    transform: scale(1.03) !important;
    filter: brightness(1.15) !important;
    background-image: url('../../assets/IncomingInspectionDashboard/button_bac.png') !important;
    background-size: 100% 100% !important;
    background-repeat: no-repeat !important;
    background-position: center !important;
    background-color: transparent !important;
    border: none !important;
    color: #ffffff !important;
    box-shadow: none !important;
  }

  &:active {
    transform: scale(0.98) !important;
    background-image: url('../../assets/IncomingInspectionDashboard/button_bac.png') !important;
    background-size: 100% 100% !important;
    background-repeat: no-repeat !important;
    background-position: center !important;
    background-color: transparent !important;
    border: none !important;
    color: #ffffff !important;
  }

  &:focus {
    background-image: url('../../assets/IncomingInspectionDashboard/button_bac.png') !important;
    background-size: 100% 100% !important;
    background-repeat: no-repeat !important;
    background-position: center !important;
    background-color: transparent !important;
    border: none !important;
    color: #ffffff !important;
    box-shadow: none !important;
    outline: none !important;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 全局覆盖猪齿鱼按钮样式
:global {
  .exportBtn {
    &.c7n-pro-btn-wrapper {
      background-image: url('../../assets/firstArticleInspectionDashboardAss/button_bac.png') !important;
      background-size: 100% 100% !important;
      background-repeat: no-repeat !important;
      background-position: center !important;
      background-color: transparent !important;
      border: none !important;
      color: #ffffff !important;
      box-shadow: none !important;
      border-radius: 0 !important;
      outline: none !important;
      height: 32px !important;
      height: 26px !important;
      line-height: 36px !important;
      font-size: 12px !important;
      padding: 0 20px !important;
      min-width: 74px !important;
      max-width: 110px !important;
    }

    &.c7n-pro-btn {
      background-image: url('../../assets/firstArticleInspectionDashboardAss/button_bac.png') !important;
      background-size: 100% 100% !important;
      background-repeat: no-repeat !important;
      background-position: center !important;
      background-color: transparent !important;
      border: none !important;
      color: #ffffff !important;
      box-shadow: none !important;
      border-radius: 0 !important;
      outline: none !important;
      height: 26px !important;
      line-height: 36px !important;
      font-size: 12px !important;
      padding: 0 20px !important;
      min-width: 74px !important;
      max-width: 110px !important;
    }

    &.c7n-pro-btn-raised {
      box-shadow: none !important;
    }

    &.c7n-pro-btn-default {
      background-image: url('../../assets/firstArticleInspectionDashboardAss/button_bac.png') !important;
      background-size: 100% 100% !important;
      background-repeat: no-repeat !important;
      background-position: center !important;
      background-color: transparent !important;
      border: none !important;
      color: #ffffff !important;
      box-shadow: none !important;
      border-radius: 0 !important;
      height: 26px !important;
      line-height: 36px !important;
      font-size: 12px !important;
      padding: 0 20px !important;
      min-width: 74px !important;
      max-width: 110px !important;
    }

    &:hover {
      background-image: url('../../assets/firstArticleInspectionDashboardAss/button_bac.png') !important;
      background-size: 100% 100% !important;
      background-repeat: no-repeat !important;
      background-position: center !important;
      background-color: transparent !important;
      border: none !important;
      color: #ffffff !important;
      box-shadow: none !important;
      transform: scale(1.03) !important;
      filter: brightness(1.15) !important;
    }

    &:active {
      background-image: url('../../assets/firstArticleInspectionDashboardAss/button_bac.png') !important;
      background-size: 100% 100% !important;
      background-repeat: no-repeat !important;
      background-position: center !important;
      background-color: transparent !important;
      border: none !important;
      color: #ffffff !important;
      transform: scale(0.98) !important;
    }

    &:focus {
      background-image: url('../../assets/firstArticleInspectionDashboardAss/button_bac.png') !important;
      background-size: 100% 100% !important;
      background-repeat: no-repeat !important;
      background-position: center !important;
      background-color: transparent !important;
      border: none !important;
      color: #ffffff !important;
      box-shadow: none !important;
      outline: none !important;
    }
  }
}

.fullscreen {
  .header {
    height: 84px !important;
    padding: 0 30px 30px 30px !important;
  }

  .mainContent {
    padding: 0px 20px 20px 20px !important;
  }

  .panelTitle {
    font-size: 17px !important;
    padding: 2px 0 0 10px !important;
  }

  .tableHeader,
  .tableHeader2 {
    font-size: 13px !important;
    padding: 10px !important;

    > span {
      padding: 0 2px !important;
    }
  }

  .tableRow,
  .tableRow2 {
    font-size: 12px !important;
    padding: 15px !important;
    color: #dde0e9 !important;
  }

  .fullscreenButton {
    width: 30px !important;
    height: 30px !important;
    margin-left: 10px !important;

    :global(.icon) {
      font-size: 24px !important;
    }
  }

  .headerRight {
    margin-right: 3% !important;
    margin-top: 4px;
  }

  .time {
    font-size: 20px !important;
  }

  .date,
  .week {
    font-size: 15px !important;
  }

  .datePickerWrapper {
    width: 168px !important;
    height: 35px !important;
  }

  .title {
    height: 30px !important;
    width: 370px !important;
  }

  .enTitle {
    font-size: 14px !important;
    padding-top: 6px !important;
  }

  .tableIcon {
    width: 45px !important;
    height: 18px !important;
    margin: 0px 8px !important;
  }

  .tableIconContainer {
    font-size: 12px !important;
    line-height: 24px !important;
  }

  .assetButton {
    padding: 8px 40px !important;
    font-size: 16px !important;
    width: 100px !important;
    height: 35px !important;
  }

  .topRow {
    gap: 24px !important;
    height: 62% !important;
  }

  .bottomRow {
    margin-top: 10px !important;
    height: 38% !important;
  }

  .chartPanelBody {
    height: calc(100% - 60px) !important;
    max-height: calc(50vh - 60px) !important;
  }

  .tableBody {
    max-height: 450px !important;
  }

  .bottomTableBody {
    max-height: 470px !important;
  }

  .panelBody {
    padding: 0 !important;
  }

  .tableHeader,
  .tableRow {
    gap: 20px !important;
  }

  .tableHeader2,
  .tableRow2 {
    gap: 40px !important;
  }

  .panelHeader {
    height: 36px !important;
    margin-bottom: 10px !important;
  }

  .timeWrapper {
    gap: 20px !important;
    padding: 0 !important;
  }

  .headerCenter {
    top: 38px !important;
  }

  // 全屏模式下的导出按钮尺寸
  .exportBtn {
    height: 26px !important;
    line-height: 36px !important;
    font-size: 12px !important;
    padding: 0 20px !important;
    min-width: 74px !important;
    max-width: 110px !important;
  }
}

// 主容器样式
.dashboardContainer {
  background-color: #0d1224;
  color: #fff;
  height: 100vh;
  position: relative;
  box-sizing: border-box;
  font-family: 'Microsoft YaHei', sans-serif;
}

// 进入全屏时确保图表容器有足够高度
:global(#finishedProductInspectionDashboard:fullscreen) .chartPanelBody,
:global(#finishedProductInspectionDashboard:-webkit-full-screen) .chartPanelBody,
:global(#finishedProductInspectionDashboard:-moz-full-screen) .chartPanelBody,
:global(#finishedProductInspectionDashboard:-ms-fullscreen) .chartPanelBody {
  height: 90vh !important;
  max-height: 90vh !important;
  min-height: 400px !important;
}

// 头部样式
.headerFullscreen {
  height: 60px !important;
}

.headerNormal {
  height: 50px;
}

.headerCenterFullscreen {
  top: 80% !important;
}

.headerCenterNormal {
  top: 58%;
}

// 日期选择器样式
.datePickerTransparent {
  background: transparent !important;
  border: none !important;
}

// 加载状态样式
.loadingContainer {
  color: #4a90e2;
  font-size: 14px;
  margin-left: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.loadingSpinner {
  width: 16px;
  height: 16px;
  border: 2px solid #4a90e2;
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 面板标题字号
.panelTitleNormal {
  font-size: 12px !important;
}

.panelTitleFullscreen {
  font-size: 14px !important;
}

// 面板体样式
.panelBodyNoPadding {
  padding: 0 !important;
}

// 图表容器样式 - 合并相似样式
.chartContainer,
.chartContainerWithMaxHeight {
  width: 100%;
  height: 100%;
}

.chartContainer {
  min-height: 220px;
}

.chartContainerWithMaxHeight {
  max-height: 100%;
}

// 底部面板样式
.bottomPanelPadding {
  padding-top: 10px !important;
}

// 可点击链接样式
.inspectionLink {
  color: #c3d1ff !important;
}

// 表格图标容器样式 - 合并基础样式
.tableIconContainer,
.tableIconContainerBold {
  font-size: 8px;
  color: #fff;
  line-height: 20px;
}

.tableIconContainerBold {
  font-weight: bold;
}

.tableIconMargin {
  margin: 0 8px !important;
}

.materialFilterControls {
  padding: 12px 10px;
  border-bottom: 1px solid rgba(74, 144, 226, 0.3);
  flex-shrink: 0;
}

.filterRow {
  display: flex;
  align-items: center;

  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
}

.filterItem {
  display: flex;
  align-items: center;
  gap: 4px;
  width: 160px;
}

.filterLabel {
  color: #c3e1e9;
  font-size: 12px;
}
