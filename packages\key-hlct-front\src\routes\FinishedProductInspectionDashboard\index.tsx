/*
 * @Author: 47844 <EMAIL>
 * @Date: 2025-08-11 14:09:30
 * @LastEditors: 47844 <EMAIL>
 * @LastEditTime: 2025-08-20 16:44:52
 * @FilePath: \inja-qms-front\packages\key-hlct-front\src\routes\FinishedProductInspectionDashboard\index.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/*
 * @Description: 成品检验实时看板
 */
import React, { useEffect, useState, useRef, useMemo, useCallback } from 'react';
import moment from 'moment';
import * as echarts from 'echarts';
import 'echarts-gl';
import { DatePicker } from 'choerodon-ui/pro';
import { Icon } from 'choerodon-ui';
import { getCurrentOrganizationId } from 'utils/utils';
import styles from './index.module.less';
import './datepicker-fix.module.less';
import ECharts from './components/ECharts';
import { getPie3D, getParametricEquation } from './components/pieChartHelper';
import { BASIC } from '@utils/config';
import ExcelExport from 'components/ExcelExport';
import tableTitlePng from '../../assets/firstArticleInspectionDashboardAss/table_title.png';
import contentBottomTitleBac from '../../assets/firstArticleInspectionDashboardAss/content-bottom-title-bac.png';
import groupTitleLong from '../../assets/FinishedProductInspectionDashboardAss/group_title_long.png';
import { dashboardService } from './services';
import {
  OverflowScroller,
  usePaginatedTableScroll,
} from './components';

import type {
  ProgressStatsData,
  AreaStatsData,
  NcStatsData,
  NcAllStatsData,
} from './services';

interface ExtendedNcStatsData extends NcStatsData {
  isNewlyLoaded?: boolean;
}

interface ExtendedNcAllStatsData extends NcAllStatsData {
  material?: string;
  lot?: string;
  areaName?: string;
  actualEndTime?: string | null;
  reviewTime?: string | null;
  isNewlyLoaded?: boolean;
}

const FinishedProductInspectionDashboard = () => {
  const [formattedTime, setFormattedTime] = useState(moment().format('YYYY-MM-DD HH:mm:ss'));
  const [week, setWeek] = useState(moment().format('dddd'));

  const [startDate, setStartDate] = useState(moment().subtract(1, 'month').startOf('day'));
  const [endDate, setEndDate] = useState(moment().endOf('day'));
  const [isDataLoading, setIsDataLoading] = useState(false);

  const [activeDefectiveItem, setActiveDefectiveItem] = useState<string | null>(null);

  // 接口数据状态
  const [progressStats, setProgressStats] = useState<ProgressStatsData>({
    pending: 0,
    overdue: 0,
    inProgress: 0,
    completed: 0,
  });
  const [areaStats, setAreaStats] = useState<AreaStatsData[]>([]);

  // 分页数据状态管理
  const [ncPageData, setNcPageData] = useState<ExtendedNcStatsData[]>([]);
  const [ncPageInfo, setNcPageInfo] = useState<{
    current: number;
    pageSize: number;
    total: number;
    hasMore: boolean;
    loading: boolean;
  }>({
    current: 0,
    pageSize: 20,
    total: 0,
    hasMore: true,
    loading: false,
  });

  const [ncAllPageData, setNcAllPageData] = useState<ExtendedNcAllStatsData[]>([]);
  const [ncAllPageInfo, setNcAllPageInfo] = useState<{
    current: number;
    pageSize: number;
    total: number;
    hasMore: boolean;
    loading: boolean;
  }>({
    current: 0,
    pageSize: 20,
    total: 0,
    hasMore: true,
    loading: false,
  });

  // 全屏状态管理
  const [isFullscreen, setIsFullscreen] = useState(false);

  const tableScrollRef = useRef<HTMLDivElement>(null);
  const ncTableScrollRef = useRef<HTMLDivElement>(null);
  const pieChartRef = useRef<HTMLDivElement>(null);

  const isInitializedRef = useRef(false);

  // 分页加载函数（支持强制刷新第一页）
  const loadMoreNcData = async (force: boolean = false) => {
    const shouldSkip = ncPageInfo.loading || !ncPageInfo.hasMore;
    if (shouldSkip && !force) return;

    const pageToLoad = force ? 0 : ncPageInfo.current;
    setNcPageInfo(prev => ({ ...prev, loading: true }));

    try {
      const response = await dashboardService.getNcStats(
        startDate.format('YYYY-MM-DD'),
        endDate.format('YYYY-MM-DD'),
        pageToLoad,
        ncPageInfo.pageSize
      );

      if (response.content && response.content.length > 0) {
        const newData: ExtendedNcAllStatsData[] = response.content.map(item => ({ ...item, isNewlyLoaded: true }));

        setNcPageData(prev => (force ? newData : [...prev, ...newData]));
        setNcPageInfo(prev => ({
          ...prev,
          current: pageToLoad + 1,
          loading: false,
          hasMore: response.number < response.totalPages - 1,
          total: response.totalElements,
        }));

        // 移除新加载标记，触发动画
        setTimeout(() => {
          setNcPageData(current => current.map(item => ({ ...item, isNewlyLoaded: false })));
        }, 100);
      } else {
        setNcPageInfo(prev => ({ ...prev, loading: false, hasMore: false }));
      }
    } catch (error) {
      console.error('加载不良项目数据失败:', error);
      setNcPageInfo(prev => ({ ...prev, loading: false }));
    }
  };

  const loadMoreNcAllData = async (force: boolean = false) => {
    const shouldSkip = ncAllPageInfo.loading || !ncAllPageInfo.hasMore;
    if (shouldSkip && !force) return;

    setNcAllPageInfo(prev => ({ ...prev, loading: true }));

    try {
      const pageToLoad = force ? 0 : ncAllPageInfo.current;
      const response = await dashboardService.getNcAllStats(
        startDate.format('YYYY-MM-DD'),
        endDate.format('YYYY-MM-DD'),
        pageToLoad,
        ncAllPageInfo.pageSize
      );

      if (response.content && response.content.length > 0) {
        const newData = response.content.map(item => ({ ...item, isNewlyLoaded: true }));

        setNcAllPageData(prev => (force ? newData : [...prev, ...newData]));
        setNcAllPageInfo(prev => ({
          ...prev,
          current: pageToLoad + 1,
          loading: false,
          hasMore: response.number < response.totalPages - 1,
          total: response.totalElements,
        }));

        // 移除新加载标记，触发动画
        setTimeout(() => {
          setNcAllPageData(current => current.map(item => ({ ...item, isNewlyLoaded: false })));
        }, 100);
      } else {
        setNcAllPageInfo(prev => ({ ...prev, loading: false, hasMore: false }));
      }
    } catch (error) {
      console.error('加载不良信息汇总数据失败:', error);
      setNcAllPageInfo(prev => ({ ...prev, loading: false }));
    }
  };

  const loadAllData = useCallback(async () => {
    setIsDataLoading(true);

    try {
      // 重置分页数据
      setNcPageData([]);
      setNcPageInfo({
        current: 0,
        pageSize: 20,
        total: 0,
        hasMore: true,
        loading: false,
      });

      setNcAllPageData([]);
      setNcAllPageInfo({
        current: 0,
        pageSize: 20,
        total: 0,
        hasMore: true,
        loading: false,
      });

      // 并行加载所有数据
      const [progressResponse, areaResponse] = await Promise.all([
        dashboardService.getProgressStats(
          startDate.format('YYYY-MM-DD'),
          endDate.format('YYYY-MM-DD')
        ),
        dashboardService.getAreaStats(
          startDate.format('YYYY-MM-DD'),
          endDate.format('YYYY-MM-DD')
        ),
      ]);

      setProgressStats(progressResponse);
      setAreaStats(areaResponse);

      // 加载第一页数据（强制刷新，避免异步状态导致跳过）
      await Promise.all([
        loadMoreNcData(true),
        loadMoreNcAllData(true),
      ]);

      console.log('所有数据加载完成');
    } catch (error) {
      console.error('数据加载失败:', error);
    } finally {
      setIsDataLoading(false);
    }
  }, [startDate, endDate]);

  const toggleFullscreen = () => {
    const dashboardElement = document.getElementById('finishedProductInspectionDashboard');

    if (!isFullscreen) {
      // 进入全屏模式
      if (dashboardElement) {
        if (dashboardElement.requestFullscreen) {
          dashboardElement.requestFullscreen();
        } else if ((dashboardElement as any).mozRequestFullScreen) {
          (dashboardElement as any).mozRequestFullScreen();
        } else if ((dashboardElement as any).msRequestFullscreen) {
          (dashboardElement as any).msRequestFullscreen();
        } else if ((dashboardElement as any).webkitRequestFullscreen) {
          (dashboardElement as any).webkitRequestFullscreen();
        }
      }
    } else {
      // 退出全屏模式
      if (document.exitFullscreen) {
        document.exitFullscreen();
      } else if ((document as any).msExitFullscreen) {
        (document as any).msExitFullscreen();
      } else if ((document as any).mozCancelFullScreen) {
        (document as any).mozCancelFullScreen();
      } else if ((document as any).webkitExitFullscreen) {
        (document as any).webkitExitFullscreen();
      }
    }
  };

  // 监听全屏状态变化
  useEffect(() => {
    const handleFullscreenChange = () => {
      const isCurrentlyFullscreen = !!(
        document.fullscreenElement ||
        (document as any).webkitFullscreenElement ||
        (document as any).mozFullScreenElement ||
        (document as any).msFullscreenElement
      );
      setIsFullscreen(isCurrentlyFullscreen);
    };

    // 添加全屏状态变化监听器
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', handleFullscreenChange);

    return () => {
      // 清理监听器
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
      document.removeEventListener('MSFullscreenChange', handleFullscreenChange);
    };
  }, []);

  useEffect(() => {
    if (!isInitializedRef.current) {
      isInitializedRef.current = true;
      loadAllData();
    } else {
      const timer = setTimeout(() => {
        loadAllData();
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [startDate, endDate, loadAllData]);

  const pieChartData = useMemo(() => [
    { name: '检验完成', value: progressStats.completed, itemStyle: { color: '#50e3c2' } },
    { name: '待检验', value: progressStats.pending, itemStyle: { color: '#f5a623' } },
    { name: '检验中', value: progressStats.inProgress, itemStyle: { color: '#4a90e2' } },
    { name: '超期待检', value: progressStats.overdue, itemStyle: { color: '#d0021b' } },
  ], [progressStats]);

  // 车间合格率图表数据
  const areaStatsData = useMemo(() => {
    if (!areaStats.length) {
      return {
        categories: [''],
        counts: [{ value: 0, itemStyle: { color: 'transparent' } }],
      };
    }

    const categories = ['', ...areaStats.map(item => item.name), ''];
    const counts = [
      { value: 0, itemStyle: { color: 'transparent' } },
      ...areaStats.map(item => item.count),
      { value: 0, itemStyle: { color: 'transparent' } },
    ];

    return { categories, counts };
  }, [areaStats]);

  // 使用分页滚动hook
  usePaginatedTableScroll(
    ncTableScrollRef,
    ncPageData,
    ncPageInfo,
    loadMoreNcData,
    2000,
  );

  const onAreaChartClick = (params: any) => {
    if (params.componentType === 'series' && params.seriesType === 'bar') {
      const areaName = params.name;
      if (!areaName) return;
      setActiveDefectiveItem(areaName);
      setTimeout(() => {
        setActiveDefectiveItem(null);
      }, 5000);
    }
  };

  useEffect(() => {
    const timer = setInterval(() => {
      setFormattedTime(moment().format('YYYY-MM-DD HH:mm:ss'));
      setWeek(moment().format('dddd'));
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  // 3D饼图初始化
  useEffect(() => {
    const el = pieChartRef.current;
    if (!el) return;

    let myChart: echarts.ECharts | null = null;
    let ro: ResizeObserver | null = null;

    const canInit = () => el.clientWidth > 0 && el.clientHeight > 0;

    const init = () => {
      if (myChart) return;
      myChart = echarts.init(el);
      const option: any = getPie3D(pieChartData, 0.8);
 
      if (option.grid3D) {
        option.grid3D.center = isFullscreen ? ['50%', '43%'] : ['50%', '38%'];
        option.grid3D.top = isFullscreen ? '-7%' : '-12%';
      }

      const optionData = pieChartData.map(item => ({
        name: item.name,
        value: item.value,
        itemStyle: item.itemStyle,
      }));

      option.legend = {
        show: true,
        bottom: '2%',
        left: 'center',
        textStyle: {
          color: '#fff',
          fontSize: 8,
        },
        itemGap: 20,
        itemWidth: 15,
        itemHeight: 15,
        data: optionData.map(item => ({
          name: item.name,
          icon: 'rect',
        })),
      };

      option.series.push({
        name: 'pie2d',
        type: 'pie',
        labelLine: {
          show: true,
          length: 22,
          length2: 44,
          smooth: false,
          lineStyle: {
            color: '#fff',
            width: 1,
          },
        },
        startAngle: -20,
        clockwise: false,
        radius: ['40%', '50%'],
        center: isFullscreen ? ['50%', '43%'] : ['50%', '38%'],
        data: optionData,
        itemStyle: {
          opacity: 0,
        },
        label: {
          show: true,
          position: 'outside',
          distanceToLabelLine: 5,
          alignTo: 'labelLine',
          bleedMargin: 5,
          overflow: 'break',
          formatter: (params: any) => {
            const item = optionData.find(i => i.name === params.name);
            if (!item) return '';
            const value = item.value;
            const colorKey = `color_${params.dataIndex}`;
            return `{${colorKey}|${value}}\n{name|${params.name}}`;
          },
          rich: (() => {
            const richConfig: any = {
              name: {
                color: '#fff',
                fontSize: 8,
                lineHeight: 12,
              },
            };
            optionData.forEach((item, index) => {
              richConfig[`color_${index}`] = {
                fontSize: 10,
                lineHeight: 14,
                fontWeight: 'bold',
                color: item.itemStyle?.color || '#fff',
              };
            });
            return richConfig;
          })(),
        },
        minAngle: 5,
      });

      myChart.setOption(option);

      let selectedIndex = '';
      let hoveredIndex = '';

      // 点击事件
      myChart.on('click', function(params: any) {
        if (!myChart) return;
        const seriesIndex = params.seriesIndex;
        if (params.seriesName === 'pie2d') return;
        if (option.series[seriesIndex] && option.series[seriesIndex].pieStatus) {
          const isSelected = !option.series[seriesIndex].pieStatus.selected;
          const isHovered = option.series[seriesIndex].pieStatus.hovered;
          const k = option.series[seriesIndex].pieStatus.k;
          const startRatio = option.series[seriesIndex].pieData.startRatio;
          const endRatio = option.series[seriesIndex].pieData.endRatio;

          if (selectedIndex !== '' && selectedIndex !== `${seriesIndex}`) {
            const oldSelectedIndex = parseInt(selectedIndex, 10);
            option.series[oldSelectedIndex].parametricEquation = getParametricEquation(
              option.series[oldSelectedIndex].pieData.startRatio,
              option.series[oldSelectedIndex].pieData.endRatio,
              false,
              false,
              k,
              option.series[oldSelectedIndex].pieData.value,
            );
            option.series[oldSelectedIndex].pieStatus.selected = false;
          }

          option.series[seriesIndex].parametricEquation = getParametricEquation(
            startRatio,
            endRatio,
            isSelected,
            isHovered,
            k,
            option.series[seriesIndex].pieData.value,
          );
          option.series[seriesIndex].pieStatus.selected = isSelected;

          if (isSelected) {
            selectedIndex = `${seriesIndex}`;
          } else {
            selectedIndex = '';
          }
          myChart.setOption(option);
        }
      });

      // 鼠标悬停事件
      myChart.on('mouseover', function(params: any) {
        if (!myChart) return;
        const seriesIndex = params.seriesIndex;
        if (hoveredIndex === `${seriesIndex}`) {
          return;
        }

        if (hoveredIndex !== '') {
          const oldHoveredIndex = parseInt(hoveredIndex, 10);
          if (option.series[oldHoveredIndex] && option.series[oldHoveredIndex].pieStatus) {
            const isSelected = option.series[oldHoveredIndex].pieStatus.selected;
            const k = option.series[oldHoveredIndex].pieStatus.k;
            option.series[oldHoveredIndex].parametricEquation = getParametricEquation(
              option.series[oldHoveredIndex].pieData.startRatio,
              option.series[oldHoveredIndex].pieData.endRatio,
              isSelected,
              false,
              k,
              option.series[oldHoveredIndex].pieData.value,
            );
            option.series[oldHoveredIndex].pieStatus.hovered = false;
            hoveredIndex = '';
          }
        }

        if (params.seriesName !== 'mouseoutSeries' && params.seriesName !== 'pie2d') {
          if (option.series[seriesIndex] && option.series[seriesIndex].pieStatus) {
            const isSelected = option.series[seriesIndex].pieStatus.selected;
            const k = option.series[seriesIndex].pieStatus.k;
            option.series[seriesIndex].parametricEquation = getParametricEquation(
              option.series[seriesIndex].pieData.startRatio,
              option.series[seriesIndex].pieData.endRatio,
              isSelected,
              true,
              k,
              option.series[seriesIndex].pieData.value + 5,
            );
            option.series[seriesIndex].pieStatus.hovered = true;
            hoveredIndex = `${seriesIndex}`;
          }
        }
        myChart.setOption(option);
      });

      // 鼠标离开事件
      myChart.on('globalout', function() {
        if (!myChart) return;
        if (hoveredIndex !== '') {
          const oldHoveredIndex = parseInt(hoveredIndex, 10);
          if (option.series[oldHoveredIndex] && option.series[oldHoveredIndex].pieStatus) {
            const isSelected = option.series[oldHoveredIndex].pieStatus.selected;
            const k = option.series[oldHoveredIndex].pieStatus.k;
            option.series[oldHoveredIndex].parametricEquation = getParametricEquation(
              option.series[oldHoveredIndex].pieData.startRatio,
              option.series[oldHoveredIndex].pieData.endRatio,
              isSelected,
              false,
              k,
              option.series[oldHoveredIndex].pieData.value,
            );
            option.series[oldHoveredIndex].pieStatus.hovered = false;
            hoveredIndex = '';
          }
        }
        myChart.setOption(option);
      });

      // 窗口大小变化时重新调整图表
      const handleResize = () => {
        if (myChart) {
          myChart.resize();
          // 全屏切换时重新调整图表中心位置
          const currentOption = myChart.getOption();
          if (currentOption.grid3D && currentOption.grid3D[0]) {
            currentOption.grid3D[0].center = isFullscreen ? ['50%', '48%'] : ['50%', '43%'];
            currentOption.grid3D[0].top = isFullscreen ? '-2%' : '-7%';
          }
          // 调整2D饼图中心位置
          if (currentOption.series) {
            const pie2dSeries = currentOption.series.find((s: any) => s.name === 'pie2d');
              if (pie2dSeries) {
                pie2dSeries.center = isFullscreen ? ['50%', '48%'] : ['50%', '43%'];
              }
          }
          myChart.setOption(currentOption);
        }
      };

      // 全屏状态变化时立即调整图表
      const handleFullscreenChange = () => {
        setTimeout(() => {
          if (myChart) {
            const currentOption = myChart.getOption();
            // 调整3D图表中心位置
            if (currentOption.grid3D && currentOption.grid3D[0]) {
              currentOption.grid3D[0].center = isFullscreen ? ['50%', '48%'] : ['50%', '43%'];
              currentOption.grid3D[0].top = isFullscreen ? '-2%' : '-7%';
            }
            // 调整2D饼图中心位置
            if (currentOption.series) {
              const pie2dSeries = currentOption.series.find((s: any) => s.name === 'pie2d');
              if (pie2dSeries) {
                pie2dSeries.center = isFullscreen ? ['50%', '48%'] : ['50%', '43%'];
              }
            }
            myChart.setOption(currentOption);
            myChart.resize();
          }
        }, 100);
      };

      window.addEventListener('resize', handleResize);
      document.addEventListener('fullscreenchange', handleFullscreenChange);

      setTimeout(() => myChart && myChart.resize(), 50);

      return () => {
        window.removeEventListener('resize', handleResize);
        document.removeEventListener('fullscreenchange', handleFullscreenChange);
        myChart?.dispose();
      };
    };

    if (canInit()) {
      return init();
    }

    // 等待容器尺寸就绪
    ro = new ResizeObserver(() => {
      if (canInit()) {
        ro && ro.disconnect();
        ro = null;
        init();
      }
    });
    ro.observe(el);

    return () => {
      ro && ro.disconnect();
      ro = null;
    };
  }, [pieChartData, isFullscreen]);

  // 车间合格率图表配置
  const areaStatsOption = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999',
        },
      },
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#777',
      textStyle: {
        color: '#fff',
      },
      formatter: (params: any) => {
        const validParams = params.filter((param: any) => {
          return param.name !== '' && param.value !== null && param.value !== undefined;
        });
        if (validParams.length === 0) return '';

        const name = validParams[0].name;
        let result = `${name}<br/>`;

        validParams.forEach((param: any) => {
          const marker = param.marker;
          const seriesName = param.seriesName;
          let value = param.value;

          if (typeof value === 'object' && value.value !== undefined) {
            value = value.value;
          }

          if (seriesName === '合格率') {
            result += `${marker}${seriesName}: ${value}%<br/>`;
          }
        });

        return result;
      },
    },
    grid: {
      left: '10%',
      right: '10%',
      bottom: '20%',
      top: '10%',
      containLabel: true,
    },
    legend: {
      data: ['合格率'],
      textStyle: {
        color: '#fff',
        fontSize: 10,
      },
      itemGap: 20,
      bottom: '8%',
    },
    xAxis: [
      {
        type: 'category',
        data: areaStatsData.categories,
        axisPointer: {
          type: 'shadow',
        },
        axisLabel: {
          color: 'rgba(255,255,255,0.5)',
          interval: 0,
          fontSize: 8,
          rotate: areaStatsData.categories.some(c => c.length > 5) ? 30 : 0,
          align: 'center',
        },
        axisTick: {
          alignWithLabel: true,
          lineStyle: {
            color: 'rgba(255,255,255,0.15)',
          },
        },
        axisLine: {
          lineStyle: {
            color: 'rgba(255,255,255,0.15)',
          },
        },
        boundaryGap: true,
        splitLine: {
          show: false,
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '',
        min: 0,
        max: 100,
        axisLabel: {
          color: 'rgba(255,255,255,0.5)',
          formatter: function(value) {
            return `${value}%`;
          },
          fontSize: 8,
        },
        axisLine: {
          lineStyle: {
            color: 'rgba(255,255,255,0.15)',
          },
        },
        axisTick: {
          lineStyle: {
            color: 'rgba(255,255,255,0.15)',
          },
        },
        splitLine: {
          show: false,
        },
      },
    ],
    series: [
      {
        name: '合格率',
        type: 'bar',
        yAxisIndex: 0,
        barWidth: '40%',
        barCategoryGap: '30%',
        showBackground: true,
        backgroundStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(180,180,180,0.18)' },
              { offset: 1, color: 'rgba(80,80,80,0.08)' }
            ]
          },
          borderRadius: [4, 4, 0, 0]
        },
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
            { offset: 0, color: 'rgba(255, 154, 46, 0.2)' },
            { offset: 0.5, color: 'rgba(255, 154, 46, 0.8)' },
            { offset: 1, color: '#ff9a2e' },
          ]),
          borderRadius: [4, 4, 0, 0],
          shadowColor: 'rgba(255, 154, 46, 0.3)',
          shadowBlur: 10,
          shadowOffsetY: 2,
        },
        label: {
          show: true,
          position: 'top',
          formatter: function(params) {
            return `${params.value}%`;
          },
          color: '#fff',
          fontSize: 8,
          fontWeight: 'bold',
        },
        tooltip: {
          valueFormatter(value: number) {
            return `${value}%`;
          },
        },
        data: areaStatsData.counts,
      },
    ],
  };

  // 导出参数
  const getExportParams = () => {
    const start = startDate.format('YYYY-MM-DD 00:00:00');
    const end = endDate.format('YYYY-MM-DD 23:59:59');
    return {
      startDate: start,
      endDate: end,
      exportType: 'DATA',
      fillerType: 'multi-sheet',
      maxDataCount: '250000',
      singleExcelMaxSheetNum: '5',
      fileType: 'EXCEL2007',
      async: 'false',
    };
  };

  return (
    <div
      id="finishedProductInspectionDashboard"
      className={`${styles.boardContainer} ${styles.dashboardContainer} ${isFullscreen ? styles.fullscreen : ''}`}
    >
      <header className={`${styles.header}`}>
        <div className={styles.headerLeft}>
          <div
            className={`${styles.datePickerWrapper}`}
          >
            <DatePicker
              className={`custom-date-picker ${styles.datePickerTransparent}`}
              popupCls="custom-datepicker-popup"
              placeholder="选择开始时间"
              value={startDate}
              onChange={date => setStartDate(date)}
              disabled={isDataLoading}
              clearButton={false}
              getPopupContainer={triggerNode => triggerNode.parentNode as HTMLElement}
            />
          </div>
          <div
            className={`${styles.datePickerWrapper}`}
          >
            <DatePicker
              className={`custom-date-picker ${styles.datePickerTransparent}`}
              popupCls="custom-datepicker-popup"
              placeholder="选择结束时间"
              value={endDate}
              onChange={date => setEndDate(date)}
              disabled={isDataLoading}
              clearButton={false}
              getPopupContainer={triggerNode => triggerNode.parentNode as HTMLElement}
            />
          </div>
          {isDataLoading && (
            <div className={styles.loadingContainer}>
              <div className={styles.loadingSpinner} />
              数据加载中...
            </div>
          )}
        </div>
        <div className={`${styles.headerCenter}`}>
          <div
            className={`${styles.title}`}
          />
          <div className={styles.enTitle}>DIGTAL VISUAL MONITORING SYSTEM</div>
        </div>
        <div className={styles.headerRight}>
          <div className={styles.timeWrapper}>
            <div className={styles.time}>{moment(formattedTime).format('HH:mm:ss')}</div>
            <div className={styles.date}>{moment(formattedTime).format('YYYY-MM-DD')}</div>
            <div className={styles.week}>{week}</div>
          </div>
          <div className={styles.fullscreenButton} onClick={toggleFullscreen}>
            <Icon type={isFullscreen ? "fullscreen_exit" : "fullscreen"} />
          </div>
        </div>
      </header>
      <main className={styles.mainContent}>
        <div className={`${styles.topRow}`}>
          <div className={styles.panel}>
            <div className={`${styles.panelHeader}`}>
              <div className={`${styles.panelTitle}`}>成品检验进度图</div>
            </div>
            <div className={`${styles.panelBody} ${styles.chartPanelBody}`}>
              <div
                className={`${styles.pieChartBackground}`}
              />
              <div ref={pieChartRef} />
            </div>
          </div>
          <div className={styles.panel}>
            <div className={`${styles.panelHeader}`} style={{ backgroundImage: `url(${groupTitleLong})`, backgroundSize: '100% 100%', backgroundRepeat: 'no-repeat', backgroundPosition: 'left center' }}>
              <div className={`${styles.panelTitle}`}>成品检验车间合格率</div>
            </div>
            <div className={`${styles.panelBody} ${styles.chartPanelBody}`}>
              <ECharts
                option={areaStatsOption}
                className={styles.chartContainerWithMaxHeight}
                onEvents={{ click: onAreaChartClick }}
                forceFullWidth
                notMerge={false}
                lazyUpdate={false}
              />
            </div>
          </div>
          <div className={styles.panel}>
            <div className={`${styles.panelHeader}`}>
              <div className={`${styles.panelTitle}`}>不良项目信息</div>
            </div>
            <div className={`${styles.panelBody}`}>
              <div className={styles.customTable}>
                <div
                  className={`${styles.tableHeader}`}
                >
                  <span>不良项目</span>
                  <span>不良单号</span>
                  <span>处置措施</span>
                  <span>下单时间</span>
                  <span>检验员</span>
                </div>
                <div className={styles.tableBody} ref={ncTableScrollRef}>
                  {(activeDefectiveItem
                    ? ncPageData.filter(item => item.inspectItemName === activeDefectiveItem)
                    : ncPageData
                  ).map(item => (
                    <div
                      className={`${styles.tableRow} ${item?.isNewlyLoaded ? styles.entering : ''}`}
                      key={`${item.inspectItemId}-${item.ncReportNum}`}
                    >
                      <OverflowScroller>{item.inspectItemName}</OverflowScroller>
                      <OverflowScroller>{item.ncReportNum}</OverflowScroller>
                      <OverflowScroller>{item.dispositionFunctionDesc || '-'}</OverflowScroller>
                      <OverflowScroller>{moment(item.creationDate).format('YYYY-MM-DD')}</OverflowScroller>
                      <OverflowScroller>{item.lastInspectorName || '-'}</OverflowScroller>
                    </div>
                  ))}
                  {ncPageInfo.loading && (
                    <div className={styles.loadingRow}>
                      <span>加载中...</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className={`${styles.bottomRow} ${isFullscreen ? styles.fullscreenBottomRow : ''}`}>
          <div className={`${styles.panel}`}>
            <div className={`${styles.panelHeader}`} style={{backgroundImage: `url(${contentBottomTitleBac})`, backgroundSize: '100% 100%', backgroundRepeat: 'no-repeat'}}>
              <div className={styles.panelTitle}>
                成品检验不良信息汇总
              </div>
              <div className={styles.panelExtra}>
                <ExcelExport
                  requestUrl={`${BASIC.TARZAN_SAMPLING}/v1/${getCurrentOrganizationId()}/qms-inspect-oqc-dashboards/nc-all-stats/export/ui`}
                  method="GET"
                  queryParams={getExportParams()}
                  buttonText="导出"
                  otherButtonProps={{
                    className: styles.exportBtn,
                    icon: undefined,
                  }}
                />
              </div>
            </div>
            <div className={`${styles.panelBody} ${styles.panelBodyNoPadding}`}>
              <div className={styles.customTable}>
                <div
                  className={`${styles.tableHeader2}`}
                  style={{ backgroundImage: `url(${tableTitlePng})`, backgroundSize: '100% 100%', backgroundRepeat: 'no-repeat', backgroundPosition: 'center' }}
                >
                  <span>序号</span>
                  <span>物料</span>
                  <span>批次</span>
                  <span>车间</span>
                  <span>不良项目</span>
                  <span>处置结果</span>
                  <span>发现时间</span>
                  <span>结束时间</span>
                </div>
                <div className={`${styles.tableBody} ${styles.bottomTableBody}`} ref={tableScrollRef}>
                  {ncAllPageData.map((item, index) => (
                    <div className={styles.tableRow2} key={`${item.inspectItemId}-${item.ncReportNum}`}>
                      <OverflowScroller>
                        <div className={`${styles.tableIcon} ${styles.bgBottomContentNum}`}>
                          <span className={styles.tableIconContainer}>{index + 1}</span>
                        </div>
                      </OverflowScroller>
                      <OverflowScroller>{item.material}</OverflowScroller>
                      <OverflowScroller>{item.lot}</OverflowScroller>
                      <OverflowScroller>{item.areaName || '-'}</OverflowScroller>
                      <OverflowScroller>{item.dispositionFunctionDesc || '-'}</OverflowScroller>
                      <OverflowScroller>{moment(item.actualEndTime).format('YYYY-MM-DD HH:mm')}</OverflowScroller>
                      <OverflowScroller>{moment(item.reviewTime).format('YYYY-MM-DD HH:mm')}</OverflowScroller>
                    </div>
                  ))}
                  {ncAllPageInfo.loading && (
                    <div className={styles.loadingRow}>
                      <span>加载中...</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default FinishedProductInspectionDashboard;