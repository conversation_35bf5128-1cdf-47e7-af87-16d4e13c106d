/*
 * @Author: 47844 <EMAIL>
 * @Date: 2025-08-01 18:07:35
 * @LastEditors: 47844 <EMAIL>
 * @LastEditTime: 2025-08-13 17:52:23
 * @FilePath: \inja-qms-front\packages\key-hlct-front\src\routes\IncomingInspectionDashboard\components\OverflowScroller.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React, { useEffect, useState, useRef } from 'react';
import styles from '../index.module.less';

interface OverflowScrollerProps {
  children: React.ReactNode;
  className?: string;
}

const OverflowScroller = React.memo(({ children, className = '' }: OverflowScrollerProps) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isOverflowing, setIsOverflowing] = useState(false);
  const [style, setStyle] = useState<any>({});

  useEffect(() => {
    const container = containerRef.current;

    if (container) {
      const checkOverflow = () => {
        const isCurrentlyOverflowing = container.scrollWidth > container.clientWidth;
        setIsOverflowing(isCurrentlyOverflowing);

        if (isCurrentlyOverflowing) {
          const overflowAmount = container.scrollWidth - container.clientWidth;
          const duration = Math.max(15, overflowAmount / 2);
          setStyle({
            '--scroll-amount': `-${overflowAmount + 16}px`,
            '--scroll-duration': `${duration}s`,
          });
        }
      };

      // 使用防抖来减少频繁的检查
      let timeoutId: NodeJS.Timeout;
      const debouncedCheckOverflow = () => {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(checkOverflow, 100);
      };

      const resizeObserver = new ResizeObserver(debouncedCheckOverflow);
      resizeObserver.observe(container);

      checkOverflow();
      document.fonts.ready.then(() => {
        checkOverflow();
      });

      return () => {
        clearTimeout(timeoutId);
        resizeObserver.disconnect();
      };
    }
  }, [children]);

  return (
    <div ref={containerRef} className={`${styles.tableCell} ${className}`}>
      <span
        className={isOverflowing ? styles.scrollingText : ''}
        style={style as React.CSSProperties}
      >
        {children}
      </span>
    </div>
  );
});

OverflowScroller.displayName = 'OverflowScroller';

export default OverflowScroller;
