import React, { useState, useEffect } from 'react';
import styles from '../index.module.less';

interface SupplierFilterContentProps {
  dataSet: any;
  onSelect: (record: any) => void;
  selectedRecord: any;
}

const SupplierFilterContent = ({
  dataSet,
  onSelect,
  selectedRecord,
}: SupplierFilterContentProps) => {
  const [localSupplierCode, setLocalSupplierCode] = useState('');
  const [localSupplierName, setLocalSupplierName] = useState('');

  useEffect(() => {
    setLocalSupplierCode('');
    setLocalSupplierName('');
    dataSet.queryDataSet?.current?.set('supplierCode', '');
    dataSet.queryDataSet?.current?.set('supplierName', '');
    dataSet.query();
  }, [dataSet]);

  const handleQuery = () => {
    dataSet.queryDataSet?.current?.set('supplierCode', localSupplierCode);
    dataSet.queryDataSet?.current?.set('supplierName', localSupplierName);
    dataSet.query();
  };

  const allRecords = dataSet && dataSet.toData ? dataSet.toData() : [];
  const filteredRecords = allRecords;

  return (
    <div className={styles.materialFilterList}>
      <div className={styles.materialFilterControls}>
        <div className={styles.filterRow}>
          <div className={styles.filterItem}>
            <label className={`${styles.filterLabel} ${styles.supplierFilterLabel}`}>供应商编码:</label>
            <input
              type="text"
              className={styles.filterInput}
              placeholder="请输入供应商编码"
              value={localSupplierCode}
              onChange={e => setLocalSupplierCode(e.target.value)}
            />
          </div>
          <div className={styles.filterItem}>
            <label className={`${styles.filterLabel} ${styles.supplierFilterLabel}`}>供应商名称:</label>
            <input
              type="text"
              className={styles.filterInput}
              placeholder="请输入供应商名称"
              value={localSupplierName}
              onChange={e => setLocalSupplierName(e.target.value)}
            />
          </div>
          <div className={styles.filterButtonBox}>
            <button
              type="button"
              className={styles.filterButton}
              onClick={() => {
                setLocalSupplierCode('');
                setLocalSupplierName('');
                dataSet.queryDataSet?.current?.set('supplierCode', '');
                dataSet.queryDataSet?.current?.set('supplierName', '');
                dataSet.query();
              }}
            >
              重置
            </button>
            <button
              type="button"
              className={styles.filterButton}
              onClick={handleQuery}
            >
              查询
            </button>
          </div>
        </div>
      </div>
      <div className={styles.materialFilterListHeader}>
        <span>供应商编码</span>
        <span>供应商名称</span>
      </div>
      <div className={styles.materialFilterListBody}>
        {filteredRecords.length > 0 ? (
          filteredRecords.map((record: any) => {
            const recordCode = record.code;
            const recordDescription = record.description;
            return (
              <div
                key={recordCode}
                className={`${styles.materialFilterListRow} ${selectedRecord?.code === recordCode ? styles.selected : ''}`}
                onClick={() => onSelect(record)}
              >
                <span>{recordCode}</span>
                <span>{recordDescription}</span>
              </div>
            );
          })
        ) : (
          <div className={styles.noDataRow}>
            <span>暂无匹配的供应商数据</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default SupplierFilterContent;
