/*
 * @Description: 来料检验实时看板
 */
import React, { useEffect, useState, useRef, useMemo, useCallback } from 'react';
import moment from 'moment';
import * as echarts from 'echarts';
import 'echarts-gl';
import { DatePicker } from 'choerodon-ui/pro';
import { Icon } from 'choerodon-ui';
import { getCurrentOrganizationId } from 'utils/utils';
import Record from 'choerodon-ui/pro/lib/data-set/Record';
import { useHistory } from 'react-router-dom';
import styles from './index.module.less';
import './datepicker-fix.module.less';
import ECharts from './components/ECharts';
import { getPie3D, getParametricEquation } from './components/pieChartHelper';
import { BASIC } from '@utils/config';
import { dashboardService } from './services';
import ExcelExport from 'components/ExcelExport';
import {
  OverflowScroller,
  MaterialFilterModal,
  SupplierFilterModal,
  usePaginatedTableScroll,
} from './components';

import type {
  ProgressStatsData,
  DefectiveStatsData,
  MaterialStatsData,
  SupplierStatsData,
} from './services';

import materialLovDataSet from './stores/MaterialFilterDS';
import supplierLovDataSet from './stores/SupplierFilterDS';

interface ExtendedSupplierStatsData extends SupplierStatsData {
  id?: number;
  isNewlyLoaded?: boolean;
}

interface ExtendedMaterialStatsData extends MaterialStatsData {
  id?: number;
  isNewlyLoaded?: boolean;
}
const IncomingInspectionDashboard = () => {
  const history = useHistory();
  const [formattedTime, setFormattedTime] = useState(moment().format('YYYY-MM-DD HH:mm:ss'));
  const [week, setWeek] = useState(moment().format('dddd'));

  const [startDate, setStartDate] = useState(moment().subtract(1, 'month').startOf('day'));
  const [endDate, setEndDate] = useState(moment().endOf('day'));
  const [isDataLoading, setIsDataLoading] = useState(false);

  const [activeDefectiveItem, setActiveDefectiveItem] = useState<string | null>(null);


  // 接口数据状态
  const [progressStats, setProgressStats] = useState<ProgressStatsData>({
    pending: 0,
    overdue: 0,
    inProgress: 0,
    completed: 0,
  });
  const [defectiveStats, setDefectiveStats] = useState<DefectiveStatsData[]>([]);

  // 分页数据状态管理
  const [defectivePageData, setDefectivePageData] = useState<any[]>([]);
  const [defectivePageInfo, setDefectivePageInfo] = useState<{
    current: number;
    pageSize: number;
    total: number;
    hasMore: boolean;
    loading: boolean;
  }>({
    current: 0, // 修改为从0开始，符合API分页规范
    pageSize: 20,
    total: 0,
    hasMore: true,
    loading: false,
  });

  const [materialPageData, setMaterialPageData] = useState<ExtendedMaterialStatsData[]>([]);
  const [materialPageInfo, setMaterialPageInfo] = useState<{
    current: number;
    pageSize: number;
    total: number;
    hasMore: boolean;
    loading: boolean;
  }>({
    current: 0,
    pageSize: 20,
    total: 0,
    hasMore: true,
    loading: false,
  });

  const [supplierPageData, setSupplierPageData] = useState<ExtendedSupplierStatsData[]>([]);
  const [supplierPageInfo, setSupplierPageInfo] = useState<{
    current: number;
    pageSize: number;
    total: number;
    hasMore: boolean;
    loading: boolean;
  }>({
    current: 0,
    pageSize: 20,
    total: 0,
    hasMore: true,
    loading: false,
  });

  const [selectedMaterial, setSelectedMaterial] = useState<Record | null>(null);
  const [selectedSupplier, setSelectedSupplier] = useState<Record | null>(null);



  const [isMaterialModalOpen, setIsMaterialModalOpen] = useState(false);
  const [tempSelectedMaterial, setTempSelectedMaterial] = useState<Record | null>(null);

  const [isSupplierModalOpen, setIsSupplierModalOpen] = useState(false);
  const [tempSelectedSupplier, setTempSelectedSupplier] = useState<Record | null>(null);

  // 全屏状态管理
  const [isFullscreen, setIsFullscreen] = useState(false);

  const tableScrollRef = useRef<HTMLDivElement>(null);
  const materialTableScrollRef = useRef<HTMLDivElement>(null);
  const supplierTableScrollRef = useRef<HTMLDivElement>(null);
  const pieChartRef = useRef<HTMLDivElement>(null);

  // 分页加载函数
  const loadMoreDefectiveData = async (force: boolean = false) => {
    const shouldSkip = defectivePageInfo.loading || !defectivePageInfo.hasMore;
    if (shouldSkip && !force) return;

    const pageToLoad = force ? 0 : defectivePageInfo.current;

    setDefectivePageInfo(prev => ({ ...prev, loading: true }));

    try {
      // 调用实际的API
      const response = await dashboardService.getDefectiveDetails(
        startDate.format('YYYY-MM-DD'),
        endDate.format('YYYY-MM-DD'),
        undefined, // 不筛选特定不良项目
        pageToLoad,
        defectivePageInfo.pageSize,
      );

      // 转换数据格式以匹配现有的显示结构
      const newData = response.content.map((item, index) => ({
        id: pageToLoad * defectivePageInfo.pageSize + index + 1,
        inspectionId: item.inspectionId,
        materialCode: item.materialCode,
        material: item.material,
        supplier: item.supplier,
        supplierCode: item.supplierCode,
        creationDate: item.creationDate,
        status: '检验完成',
        isPassed: false,
        defectiveItem: item.defectiveItem,
        inspector: item.inspector || '未知',
        arrivalBatch: `B${String(item.inspectDocId).slice(-3)}`,
        overdueDays: 0,
        isNewlyLoaded: true, // 标记新加载的数据
        inspectDocId: item.inspectDocId,
      }));

      setDefectivePageData(prev => (force ? newData : [...prev, ...newData]));
      setDefectivePageInfo(prev => ({
        ...prev,
        current: pageToLoad + 1,
        loading: false,
        hasMore: response.number < response.totalPages - 1,
        total: response.totalElements,
      }));

      // 移除新加载标记，触发动画
      setTimeout(() => {
        setDefectivePageData(current => current.map(item => ({ ...item, isNewlyLoaded: false })));
      }, 100);
    } catch (error) {
      console.error('加载不良信息数据失败:', error);
      setDefectivePageInfo(prev => ({ ...prev, loading: false }));
    }
  };

  const loadMoreMaterialData = async (force: boolean = false) => {
    const shouldSkip = materialPageInfo.loading || !materialPageInfo.hasMore;
    if (shouldSkip && !force) return;

    setMaterialPageInfo(prev => ({ ...prev, loading: true }));

    try {
      // 调用实际的API
      // 需要根据供应商编码找到对应的ID
      let supplierIdForFilter: number | undefined;
      if (selectedSupplier) {
        // 直接从已选记录中读取 supplierId
        supplierIdForFilter = typeof (selectedSupplier as any).get === 'function'
          ? (selectedSupplier as any).get('supplierId')
          : (selectedSupplier as any)['supplierId'];
      }

      const pageToLoad = force ? 0 : materialPageInfo.current;

      const response = await dashboardService.getMaterialStats(
        startDate.format('YYYY-MM-DD'),
        endDate.format('YYYY-MM-DD'),
        supplierIdForFilter, // 使用找到的供应商ID进行筛选
        pageToLoad,
        materialPageInfo.pageSize,
        undefined // 查全部时 materialId 省略
      );

      // 转换数据格式并标记新加载的数据
      const newData = response.content.map((item, index) => ({
        ...item,
        id: pageToLoad * materialPageInfo.pageSize + index + 1, // 前端生成连续序号
        isNewlyLoaded: true,
      }));

      setMaterialPageData(prev => (force ? newData : [...prev, ...newData]));
      setMaterialPageInfo(prev => ({
        ...prev,
        current: pageToLoad + 1,
        loading: false,
        hasMore: response.number < response.totalPages - 1,
        total: response.totalElements,
      }));

      // 移除新加载标记，触发动画
      setTimeout(() => {
        setMaterialPageData(current => current.map(item => ({ ...item, isNewlyLoaded: false })));
      }, 100);
    } catch (error) {
      console.error('加载物料数据失败:', error);
      setMaterialPageInfo(prev => ({ ...prev, loading: false }));
    }
  };

  const loadMoreSupplierData = async (force: boolean = false) => {
    const shouldSkip = supplierPageInfo.loading || !supplierPageInfo.hasMore;
    if (shouldSkip && !force) return;

    setSupplierPageInfo(prev => ({ ...prev, loading: true }));

    try {
      // 调用实际的API
      // 需要根据物料编码找到对应的ID
      let materialIdForFilter: number | undefined;
      if (selectedMaterial) {
        // 直接从已选记录中读取 materialId
        materialIdForFilter = typeof (selectedMaterial as any).get === 'function'
          ? (selectedMaterial as any).get('materialId')
          : (selectedMaterial as any)['materialId'];
      }

      const pageToLoad = force ? 0 : supplierPageInfo.current;

      const response = await dashboardService.getSupplierStats(
        startDate.format('YYYY-MM-DD'),
        endDate.format('YYYY-MM-DD'),
        materialIdForFilter, // 使用找到的物料ID进行筛选
        pageToLoad,
        supplierPageInfo.pageSize,
      );

      // 转换数据格式并标记新加载的数据
      const newData = response.content.map((item, index) => ({
        ...item,
        id: pageToLoad * supplierPageInfo.pageSize + index + 1, // 前端生成连续序号
        isNewlyLoaded: true,
      }));

      setSupplierPageData(prev => (force ? newData : [...prev, ...newData]));
      setSupplierPageInfo(prev => ({
        ...prev,
        current: pageToLoad + 1,
        loading: false,
        hasMore: response.number < response.totalPages - 1,
        total: response.totalElements,
      }));

      // 移除新加载标记，触发动画
      setTimeout(() => {
        setSupplierPageData(current => current.map(item => ({ ...item, isNewlyLoaded: false })));
      }, 100);
    } catch (error) {
      console.error('加载供应商数据失败:', error);
      setSupplierPageInfo(prev => ({ ...prev, loading: false }));
    }
  };


  const isInitializedRef = useRef(false);

  const loadAllData = useCallback(async () => {
    setIsDataLoading(true);

    try {
      // 清空筛选状态，确保日期变化后所有数据都基于新的日期范围重新查询
      setSelectedMaterial(null);
      setSelectedSupplier(null);

      setDefectivePageData([]);
      setDefectivePageInfo({
        current: 0,
        pageSize: 20,
        total: 0,
        hasMore: true,
        loading: false,
      });

      setMaterialPageData([]);
      setMaterialPageInfo({
        current: 0,
        pageSize: 20,
        total: 0,
        hasMore: true,
        loading: false,
      });

      setSupplierPageData([]);
      setSupplierPageInfo({
        current: 0,
        pageSize: 20,
        total: 0,
        hasMore: true,
        loading: false,
      });

      await Promise.all([
        dashboardService.getProgressStats(
          startDate.format('YYYY-MM-DD'),
          endDate.format('YYYY-MM-DD')
        ).then(data => setProgressStats(data)),

        dashboardService.getDefectiveStats(
          startDate.format('YYYY-MM-DD'),
          endDate.format('YYYY-MM-DD')
        ).then(data => setDefectiveStats(data)),

        // 直接调用API，不受筛选条件影响
        dashboardService.getDefectiveDetails(
          startDate.format('YYYY-MM-DD'),
          endDate.format('YYYY-MM-DD'),
          undefined,
          0,
          20,
        ).then(response => {
          const newData = response.content.map((item, index) => ({
            id: index + 1,
            inspectionId: item.inspectionId,
            materialCode: item.materialCode,
            material: item.material,
            supplier: item.supplier,
            supplierCode: item.supplierCode,
            creationDate: item.creationDate,
            status: '检验完成',
            isPassed: false,
            defectiveItem: item.defectiveItem,
            inspector: item.inspector || '未知',
            arrivalBatch: `B${String(item.inspectDocId).slice(-3)}`,
            overdueDays: 0,
            isNewlyLoaded: true,
            inspectDocId: item.inspectDocId,
          }));
          setDefectivePageData(newData);
          setDefectivePageInfo({
            current: 1,
            pageSize: 20,
            total: response.totalElements,
            hasMore: response.number < response.totalPages - 1,
            loading: false,
          });
          setTimeout(() => {
            setDefectivePageData(current => current.map(item => ({ ...item, isNewlyLoaded: false })));
          }, 100);
        }),

        // 直接调用API，不受筛选条件影响
        dashboardService.getMaterialStats(
          startDate.format('YYYY-MM-DD'),
          endDate.format('YYYY-MM-DD'),
          undefined, // 不传 supplierId
          0,
          20,
          undefined // 不传 materialId
        ).then(response => {
          const newData = response.content.map((item, index) => ({
            ...item,
            id: index + 1,
            isNewlyLoaded: true,
          }));
          setMaterialPageData(newData);
          setMaterialPageInfo({
            current: 1,
            pageSize: 20,
            total: response.totalElements,
            hasMore: response.number < response.totalPages - 1,
            loading: false,
          });
          setTimeout(() => {
            setMaterialPageData(current => current.map(item => ({ ...item, isNewlyLoaded: false })));
          }, 100);
        }),

        // 直接调用API，不受筛选条件影响
        dashboardService.getSupplierStats(
          startDate.format('YYYY-MM-DD'),
          endDate.format('YYYY-MM-DD'),
          undefined, // 不传 materialId，显示所有供应商
          0,
          20,
        ).then(response => {
          const newData = response.content.map((item, index) => ({
            ...item,
            id: index + 1,
            isNewlyLoaded: true,
          }));
          setSupplierPageData(newData);
          setSupplierPageInfo({
            current: 1,
            pageSize: 20,
            total: response.totalElements,
            hasMore: response.number < response.totalPages - 1,
            loading: false,
          });
          setTimeout(() => {
            setSupplierPageData(current => current.map(item => ({ ...item, isNewlyLoaded: false })));
          }, 100);
        }),
      ]);

      console.log('所有数据加载完成');
    } catch (error) {
      console.error('数据加载失败:', error);
    } finally {
      setIsDataLoading(false);
    }
  }, [startDate, endDate]);

  const toggleFullscreen = () => {
    const dashboardElement = document.getElementById('incomingInspectionDashboard');

    if (!isFullscreen) {
      // 进入全屏模式
      if (dashboardElement) {
        if (dashboardElement.requestFullscreen) {
          dashboardElement.requestFullscreen();
        } else if ((dashboardElement as any).mozRequestFullScreen) {
          (dashboardElement as any).mozRequestFullScreen();
        } else if ((dashboardElement as any).msRequestFullscreen) {
          (dashboardElement as any).msRequestFullscreen();
        } else if ((dashboardElement as any).webkitRequestFullscreen) {
          (dashboardElement as any).webkitRequestFullscreen();
        }
      }
    } else {
      // 退出全屏模式
      if (document.exitFullscreen) {
        document.exitFullscreen();
      } else if ((document as any).msExitFullscreen) {
        (document as any).msExitFullscreen();
      } else if ((document as any).mozCancelFullScreen) {
        (document as any).mozCancelFullScreen();
      } else if ((document as any).webkitExitFullscreen) {
        (document as any).webkitExitFullscreen();
      }
    }
  };

  // 监听全屏状态变化
  useEffect(() => {
    const handleFullscreenChange = () => {
      const isCurrentlyFullscreen = !!(
        document.fullscreenElement ||
        (document as any).webkitFullscreenElement ||
        (document as any).mozFullScreenElement ||
        (document as any).msFullscreenElement
      );
      setIsFullscreen(isCurrentlyFullscreen);
    };

    // 添加全屏状态变化监听器
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', handleFullscreenChange);

    return () => {
      // 清理监听器
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
      document.removeEventListener('MSFullscreenChange', handleFullscreenChange);
    };
  }, []);

  useEffect(() => {
    if (!isInitializedRef.current) {
      isInitializedRef.current = true;
      loadAllData();
    } else {
      const timer = setTimeout(() => {
        loadAllData();
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [startDate, endDate, loadAllData]);

  const pieChartData = useMemo(() => [
    { name: '检验完成', value: progressStats.completed, itemStyle: { color: '#50e3c2' } },
    { name: '待检验', value: progressStats.pending, itemStyle: { color: '#f5a623' } },
    { name: '超期待检', value: progressStats.overdue, itemStyle: { color: '#d0021b' } },
    { name: '检验中', value: progressStats.inProgress, itemStyle: { color: '#4a90e2' } },
  ], [progressStats]);

  const defectiveStatsData = useMemo(() => {
    if (!defectiveStats.length) {
      return {
        categories: [''],
        counts: [{ value: 0, itemStyle: { color: 'transparent' } }],
        ratios: [null]
      };
    }

    const categories = ['', ...defectiveStats.map(item => item.name), ''];
    const counts = [
      { value: 0, itemStyle: { color: 'transparent' } },
      ...defectiveStats.map(item => item.count),
      { value: 0, itemStyle: { color: 'transparent' } },
    ];
    const ratios = [null, ...defectiveStats.map(item => item.ratio / 100), null];

    return { categories, counts, ratios };
  }, [defectiveStats]);



  // 使用分页滚动hook
  usePaginatedTableScroll(
    tableScrollRef,
    defectivePageData,
    defectivePageInfo,
    loadMoreDefectiveData,
    2000,
  );
  usePaginatedTableScroll(
    materialTableScrollRef,
    materialPageData,
    materialPageInfo,
    loadMoreMaterialData,
    2000,
  );
  usePaginatedTableScroll(
    supplierTableScrollRef,
    supplierPageData,
    supplierPageInfo,
    loadMoreSupplierData,
    2000,
  );

  const onDefectiveChartClick = (params: any) => {
    if (params.componentType === 'series' && params.seriesType === 'bar') {
      const projectName = params.name;
      if (!projectName) return;
      setActiveDefectiveItem(projectName);
        setTimeout(() => {
        setActiveDefectiveItem(null);
        }, 5000);
    }
  };

  const handleInspectionIdClick = (inspectionId: string, inspectDocId?: number) => {
    history.push({
      pathname: '/hwms/inspection-platform/list',
      state: {
        inspectDocNum: inspectionId,
        inspectDocId,
        nowDate: Date.now(),
      },
    });
  };

  const handleMaterialSelect = (record: any) => {
    setTempSelectedMaterial(record);
  };

  const handleMaterialConfirm = async () => {
    setSelectedMaterial(tempSelectedMaterial);
    setIsMaterialModalOpen(false);

    let materialId: number | undefined;
    if (tempSelectedMaterial) {
      materialId = typeof tempSelectedMaterial.get === 'function'
        ? tempSelectedMaterial.get('materialId')
        : tempSelectedMaterial['materialId'];
    }

    // 清空之前的供应商筛选
    setSelectedSupplier(null);

    setMaterialPageData([]);
    setMaterialPageInfo({
      current: 0,
      pageSize: 20,
      total: 0,
      hasMore: true,
      loading: false,
    });

    const response = await dashboardService.getMaterialStats(
      startDate.format('YYYY-MM-DD'),
      endDate.format('YYYY-MM-DD'),
      undefined, // 不传 supplierId
      0,
      20,
      materialId // 只传 materialId
    );

    if (response?.content) {
      const newData = response.content.map((item: any, index: number) => ({
        ...item,
        id: index + 1,
        isNewlyLoaded: true,
      }));
      setMaterialPageData(newData);
      setMaterialPageInfo({
        current: 1,
        pageSize: 20,
        total: newData.length,
        hasMore: false,
        loading: false,
      });
      setTimeout(() => {
        setMaterialPageData(current =>
          current.map(item => ({ ...item, isNewlyLoaded: false }))
        );
      }, 100);
    }

    // 同步刷新“来料检验-供应商”表格，仅展示该物料对应的供应商
    setSupplierPageData([]);
    setSupplierPageInfo({
      current: 0,
      pageSize: 20,
      total: 0,
      hasMore: true,
      loading: false,
    });

    const supplierResp = await dashboardService.getSupplierStats(
      startDate.format('YYYY-MM-DD'),
      endDate.format('YYYY-MM-DD'),
      materialId, // 按所选物料过滤供应商
      0,
      20
    );

    if (supplierResp?.content) {
      const newSupplierData = supplierResp.content.map((item: any, index: number) => ({
        ...item,
        id: index + 1,
        isNewlyLoaded: true,
      }));
      setSupplierPageData(newSupplierData);
      setSupplierPageInfo({
        current: 1,
        pageSize: 20,
        total: supplierResp.totalElements,
        hasMore: supplierResp.number < supplierResp.totalPages - 1,
        loading: false,
      });
      setTimeout(() => {
        setSupplierPageData(current => current.map(item => ({ ...item, isNewlyLoaded: false })));
      }, 100);
    } else {
      setSupplierPageData([]);
      setSupplierPageInfo({
        current: 0,
        pageSize: 20,
        total: 0,
        hasMore: false,
        loading: false,
      });
    }
  };

  const handleMaterialCancel = () => {
    setIsMaterialModalOpen(false);
    setTempSelectedMaterial(null);
  };

  const handleSupplierSelect = (record: any) => {
    setTempSelectedSupplier(record);
  };

  const handleSupplierConfirm = async () => {
    setSelectedSupplier(tempSelectedSupplier);
    setIsSupplierModalOpen(false);

    let supplierId: number | undefined;
    if (tempSelectedSupplier) {
      supplierId = typeof tempSelectedSupplier.get === 'function'
        ? tempSelectedSupplier.get('supplierId')
        : tempSelectedSupplier['supplierId'];
    }

    // 清空之前的物料筛选
    setSelectedMaterial(null);

    // 供应商筛选后，供应商列表只显示被选中的供应商
    setSupplierPageData([]);
    setSupplierPageInfo({
      current: 0,
      pageSize: 20,
      total: 0,
      hasMore: true,
      loading: false,
    });

    // 查询被选中供应商的数据
    const supplierResp = await dashboardService.getSupplierStats(
      startDate.format('YYYY-MM-DD'),
      endDate.format('YYYY-MM-DD'),
      undefined, // 不传 materialId，因为我们要显示该供应商的所有数据
      0,
      20
    );

    if (supplierResp?.content) {
      // 如果没有选择供应商，显示所有数据；否则筛选出被选中的供应商数据
      const filteredSupplierData = supplierId 
        ? supplierResp.content.filter((item: any) => item.supplierId === supplierId)
        : supplierResp.content;
      
      if (filteredSupplierData.length > 0) {
        const newSupplierData = filteredSupplierData.map((item: any, index: number) => ({
          ...item,
          id: index + 1,
          isNewlyLoaded: true,
        }));
        setSupplierPageData(newSupplierData);
        setSupplierPageInfo({
          current: 1,
          pageSize: 20,
          total: newSupplierData.length,
          hasMore: false,
          loading: false,
        });
        setTimeout(() => {
          setSupplierPageData(current => current.map(item => ({ ...item, isNewlyLoaded: false })));
        }, 100);
      } else {
        setSupplierPageData([]);
        setSupplierPageInfo({
          current: 0,
          pageSize: 20,
          total: 0,
          hasMore: false,
          loading: false,
        });
      }
    }

    // 同步刷新“来料检验-物料”表格，仅展示该供应商对应的物料
    setMaterialPageData([]);
    setMaterialPageInfo({
      current: 0,
      pageSize: 20,
      total: 0,
      hasMore: true,
      loading: false,
    });

    const materialResp = await dashboardService.getMaterialStats(
      startDate.format('YYYY-MM-DD'),
      endDate.format('YYYY-MM-DD'),
      supplierId,
      0,
      20,
      undefined
    );

    if (materialResp?.content) {
      const newMaterialData = materialResp.content.map((item: any, index: number) => ({
        ...item,
        id: index + 1,
        isNewlyLoaded: true,
      }));
      setMaterialPageData(newMaterialData);
      setMaterialPageInfo({
        current: 1,
        pageSize: 20,
        total: materialResp.totalElements,
        hasMore: materialResp.number < materialResp.totalPages - 1,
        loading: false,
      });
      setTimeout(() => {
        setMaterialPageData(current => current.map(item => ({ ...item, isNewlyLoaded: false })));
      }, 100);
    } else {
      setMaterialPageData([]);
      setMaterialPageInfo({
        current: 0,
        pageSize: 20,
        total: 0,
        hasMore: false,
        loading: false,
      });
    }
  };

  const handleSupplierCancel = () => {
    setIsSupplierModalOpen(false);
    setTempSelectedSupplier(null);
  };

  useEffect(() => {
    const timer = setInterval(() => {
      setFormattedTime(moment().format('YYYY-MM-DD HH:mm:ss'));
      setWeek(moment().format('dddd'));
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  // 3D饼图初始化
  useEffect(() => {
    if (pieChartRef.current) {
      let myChart = echarts.init(pieChartRef.current);
      
      // 检查数据是否有效
      const hasValidData = pieChartData.some(item => item.value > 0);
      if (!hasValidData) {
        // 如果没有有效数据，清空图表内容
        myChart.clear();
        return;
      }
      
      const option: any = getPie3D(pieChartData, 0.8);

      // 根据全屏状态调整3D图表的中心位置
      if (option.grid3D) {
        option.grid3D.center = isFullscreen ? ['50%', '40%'] : ['50%', '35%'];
        option.grid3D.top = isFullscreen ? '-10%' : '-15%';
      }

      // 添加图例配置
      const optionData = pieChartData.map(item => ({
        name: item.name,
        value: item.value,
        itemStyle: item.itemStyle,
      }));

      // 过滤掉值为0的数据项，避免2D饼图渲染问题
      const validOptionData = optionData.filter(item => item.value > 0);

      option.legend = {
        show: true,
        bottom: '2%',
        left: 'center',
        textStyle: {
          color: '#fff',
          fontSize: 8,
        },
        itemGap: 20,
        itemWidth: 15,
        itemHeight: 15,
        data: validOptionData.map(item => ({
          name: item.name,
          icon: 'rect',
        })),
      };

      // 添加2D饼图用于标签显示
      option.series.push({
        name: 'pie2d',
        type: 'pie',
        labelLine: {
          show: true,
          length: 20,
          length2: 40,
          smooth: false,
          lineStyle: {
            color: '#fff',
            width: 1,
          },
        },
        startAngle: -20,
        clockwise: false,
        radius: ['30%', '40%'],
        center: isFullscreen ? ['50%', '40%'] : ['50%', '35%'],
        data: validOptionData,
        itemStyle: {
          opacity: 0,
        },
        label: {
          show: true,
          position: 'outside',
          distanceToLabelLine: 6,
          alignTo: 'labelLine',
          bleedMargin: 10,
          overflow: 'break',
          showMinLabel: true,
          formatter: (params: any) => {
            // 直接使用params中的值，不需要查找
            const value = params.value;
            const name = params.name;
            const colorKey = `color_${params.dataIndex}`;
            return `{${colorKey}|${value}}\n{name|${name}}`;
          },
          rich: (() => {
            const richConfig: any = {
              name: {
                color: '#fff',
                fontSize: 8,
                lineHeight: 12,
              },
            };
            validOptionData.forEach((item, index) => {
              richConfig[`color_${index}`] = {
                fontSize: 10,
                lineHeight: 14,
                fontWeight: 'bold',
                color: item.itemStyle?.color || '#fff',
              };
            });
            return richConfig;
          })(),
        },
        minAngle: 5,
        // 添加避免0值渲染问题的配置
        avoidLabelOverlap: true,
        labelLayout: {
          hideOverlap: false,
          moveOverlap: 'shiftY',
          draggable: true,
        },
      });

      myChart.setOption(option);

      let selectedIndex = '';
      let hoveredIndex = '';

      // 点击事件
      myChart.on('click', function(params: any) {
        if (!myChart) return;
        const seriesIndex = params.seriesIndex;
        if (params.seriesName === 'pie2d') return;
        if (option.series[seriesIndex] && option.series[seriesIndex].pieStatus) {
          const isSelected = !option.series[seriesIndex].pieStatus.selected;
          const isHovered = option.series[seriesIndex].pieStatus.hovered;
          const k = option.series[seriesIndex].pieStatus.k;
          const startRatio = option.series[seriesIndex].pieData.startRatio;
          const endRatio = option.series[seriesIndex].pieData.endRatio;

          if (selectedIndex !== '' && selectedIndex !== `${seriesIndex}`) {
            const oldSelectedIndex = parseInt(selectedIndex, 10);
            option.series[oldSelectedIndex].parametricEquation = getParametricEquation(
              option.series[oldSelectedIndex].pieData.startRatio,
              option.series[oldSelectedIndex].pieData.endRatio,
              false,
              false,
              k,
              option.series[oldSelectedIndex].pieData.value,
            );
            option.series[oldSelectedIndex].pieStatus.selected = false;
          }

          option.series[seriesIndex].parametricEquation = getParametricEquation(
            startRatio,
            endRatio,
            isSelected,
            isHovered,
            k,
            option.series[seriesIndex].pieData.value,
          );
          option.series[seriesIndex].pieStatus.selected = isSelected;

          if (isSelected) {
            selectedIndex = `${seriesIndex}`;
          } else {
            selectedIndex = '';
          }
          myChart.setOption(option);
        }
      });

      // 鼠标悬停事件
      myChart.on('mouseover', function(params: any) {
        if (!myChart) return;
        const seriesIndex = params.seriesIndex;
        if (hoveredIndex === `${seriesIndex}`) {
          return;
        }

        if (hoveredIndex !== '') {
          const oldHoveredIndex = parseInt(hoveredIndex, 10);
          if (option.series[oldHoveredIndex] && option.series[oldHoveredIndex].pieStatus) {
            const isSelected = option.series[oldHoveredIndex].pieStatus.selected;
            const k = option.series[oldHoveredIndex].pieStatus.k;
            option.series[oldHoveredIndex].parametricEquation = getParametricEquation(
              option.series[oldHoveredIndex].pieData.startRatio,
              option.series[oldHoveredIndex].pieData.endRatio,
              isSelected,
              false,
              k,
              option.series[oldHoveredIndex].pieData.value,
            );
            option.series[oldHoveredIndex].pieStatus.hovered = false;
            hoveredIndex = '';
          }
        }

        if (params.seriesName !== 'mouseoutSeries' && params.seriesName !== 'pie2d') {
          if (option.series[seriesIndex] && option.series[seriesIndex].pieStatus) {
            const isSelected = option.series[seriesIndex].pieStatus.selected;
            const k = option.series[seriesIndex].pieStatus.k;
            option.series[seriesIndex].parametricEquation = getParametricEquation(
              option.series[seriesIndex].pieData.startRatio,
              option.series[seriesIndex].pieData.endRatio,
              isSelected,
              true,
              k,
              option.series[seriesIndex].pieData.value + 5,
            );
            option.series[seriesIndex].pieStatus.hovered = true;
            hoveredIndex = `${seriesIndex}`;
          }
        }
        myChart.setOption(option);
      });

      // 鼠标离开事件
      myChart.on('globalout', function() {
        if (!myChart) return;
        if (hoveredIndex !== '') {
          const oldHoveredIndex = parseInt(hoveredIndex, 10);
          if (option.series[oldHoveredIndex] && option.series[oldHoveredIndex].pieStatus) {
            const isSelected = option.series[oldHoveredIndex].pieStatus.selected;
            const k = option.series[oldHoveredIndex].pieStatus.k;
            option.series[oldHoveredIndex].parametricEquation = getParametricEquation(
              option.series[oldHoveredIndex].pieData.startRatio,
              option.series[oldHoveredIndex].pieData.endRatio,
              isSelected,
              false,
              k,
              option.series[oldHoveredIndex].pieData.value,
            );
            option.series[oldHoveredIndex].pieStatus.hovered = false;
            hoveredIndex = '';
          }
        }
        myChart.setOption(option);
      });

      // 窗口大小变化时重新调整图表
      const handleResize = () => {
        if (myChart) {
          myChart.resize();
          // 全屏切换时重新调整图表中心位置
          const currentOption = myChart.getOption();
          if (currentOption.grid3D && currentOption.grid3D[0]) {
            currentOption.grid3D[0].center = isFullscreen ? ['50%', '48%'] : ['50%', '43%'];
            currentOption.grid3D[0].top = isFullscreen ? '-2%' : '-7%';
          }
          // 调整2D饼图中心位置
          if (currentOption.series) {
            const pie2dSeries = currentOption.series.find((s: any) => s.name === 'pie2d');
            if (pie2dSeries) {
              pie2dSeries.center = isFullscreen ? ['50%', '48%'] : ['50%', '43%'];
            }
          }
          myChart.setOption(currentOption);
        }
      };

      // 全屏状态变化时立即调整图表
      const handleFullscreenChange = () => {
        setTimeout(() => {
          if (myChart) {
            const currentOption = myChart.getOption();
            // 调整3D图表中心位置
            if (currentOption.grid3D && currentOption.grid3D[0]) {
              currentOption.grid3D[0].center = isFullscreen ? ['50%', '48%'] : ['50%', '43%'];
              currentOption.grid3D[0].top = isFullscreen ? '-2%' : '-7%';
            }
            // 调整2D饼图中心位置
            if (currentOption.series) {
              const pie2dSeries = currentOption.series.find((s: any) => s.name === 'pie2d');
              if (pie2dSeries) {
                pie2dSeries.center = isFullscreen ? ['50%', '48%'] : ['50%', '43%'];
              }
            }
            myChart.setOption(currentOption);
            myChart.resize();
          }
        }, 100);
      };

      window.addEventListener('resize', handleResize);
      document.addEventListener('fullscreenchange', handleFullscreenChange);

      return () => {
        window.removeEventListener('resize', handleResize);
        document.removeEventListener('fullscreenchange', handleFullscreenChange);
        if (myChart) {
          myChart.dispose();
          myChart = null;
        }
      };
    }
  }, [pieChartData, isFullscreen]);



  // 移除重复的定时器设置，已在上面定义过

  const defectiveStatsOption = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999',
        },
      },
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#777',
      textStyle: {
        color: '#fff',
      },
      formatter: (params: any) => {
        // 过滤掉空的占位项和null值
        const validParams = params.filter((param: any) => {
          return param.name !== '' && param.value !== null && param.value !== undefined;
        });
        if (validParams.length === 0) return '';

        const name = validParams[0].name;
        let result = `${name}<br/>`;

        validParams.forEach((param: any) => {
          const marker = param.marker;
          const seriesName = param.seriesName;
          let value = param.value;

          if (typeof value === 'object' && value.value !== undefined) {
            value = value.value;
          }

          if (seriesName === '不良个数') {
            result += `${marker}${seriesName}: ${value}个<br/>`;
          } else if (seriesName === '不良占比') {
            result += `${marker}${seriesName}: ${(value * 100).toFixed(2)}<br/>`;
          }
        });

        return result;
      },
    },
    grid: {
      left: '10%',
      right: '10%',
      bottom: '12%',
      top: '10%',
      containLabel: true,
    },
    legend: {
      show: true,
      bottom: '5%',
      left: 'center',
      itemGap: 20,
      itemWidth: 30,
      itemHeight: 8,
      textStyle: {
        color: '#fff',
        fontSize: 10,
      },
      data: [
        {
          name: '不良个数',
          icon:
            'image://data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="30" height="8" viewBox="0 0 30 8"><rect x="0" y="0" width="30" height="8" fill="%23ff9a2e"/></svg>',
        },
        '不良占比',
      ],
    },
    xAxis: [
      {
        type: 'category',
        data: defectiveStatsData.categories,
        axisPointer: {
          type: 'shadow',
        },
        axisLabel: {
          color: 'rgba(255,255,255,0.5)',
          interval: 0,
          fontSize: 8,
          rotate: defectiveStatsData.categories.some(c => c.length > 5) ? 30 : 0,
          align: 'center',
        },
        axisTick: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        boundaryGap: true,
        splitLine: {
          show: false,
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '',
        min: 0,
        max: function(value) {
          return value.max * 1.2;
        },
        axisLabel: {
          show: true,
          color: 'rgba(255,255,255,0.5)',
          formatter: '{value}',
          fontSize: 8,
        },
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: false,
        },
      },
      {
        type: 'value',
        name: '',
        min: 0,
        max: 1.0,
        interval: 0.1,
        axisLabel: {
          show: true,
          color: 'rgba(255,255,255,0.5)',
          formatter(value: number) {
            return `${(value * 100).toFixed(2)}`;
          },
          fontSize: 8,
        },
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: false,
        },
      },
    ],
    series: [
      {
        name: '不良个数',
        type: 'bar',
        yAxisIndex: 0,
        barWidth: '40%',
        barCategoryGap: '30%',
        showBackground: true,
        backgroundStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(180,180,180,0.18)' },
              { offset: 1, color: 'rgba(80,80,80,0.08)' }
            ]
          },
          borderRadius: [4, 4, 0, 0]
        },
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
            { offset: 0, color: 'rgba(255, 154, 46, 0.2)' },
            { offset: 0.5, color: 'rgba(255, 154, 46, 0.8)' },
            { offset: 1, color: '#ff9a2e' },
          ]),
          borderRadius: [4, 4, 0, 0],
          shadowColor: 'rgba(255, 154, 46, 0.3)',
          shadowBlur: 10,
          shadowOffsetY: 2,
        },
        label: {
          show: true,
          position: 'top',
          formatter: '{c}',
          color: '#fff',
          fontSize: 8,
          fontWeight: 'bold',
        },
        tooltip: {
          valueFormatter(value: number) {
            return `${value}个`;
          },
        },
        data: defectiveStatsData.counts,
      },
      {
        name: '不良占比',
        type: 'line',
        yAxisIndex: 1,
        smooth: true,
        symbol: 'circle',
        symbolSize: 8,
        lineStyle: {
          color: '#00ffc5',
          width: 2,
        },
        itemStyle: {
          color: '#00ffc5',
          borderColor: '#fff',
          borderWidth: 2,
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgba(0, 255, 197, 0.5)',
            },
            {
              offset: 1,
              color: 'rgba(0, 255, 197, 0)',
            },
          ]),
        },

        label: {
          show: true,
          position: 'right',
          formatter: (params: any) => {
            return params.value === null ? '' : `${(params.value * 100).toFixed(2)}%`;
          },
          color: '#fff',
          fontSize: 12,
          fontWeight: 'bold',
        },
        data: defectiveStatsData.ratios,
      },
    ],
  };

  const getExportParams = () => {
    const start = startDate.format('YYYY-MM-DD 00:00:00');
    const end = endDate.format('YYYY-MM-DD 23:59:59');
    return {
      startDate: start,
      endDate: end,
      exportType: 'DATA',
      fillerType: 'multi-sheet',
      maxDataCount: '250000',
      singleExcelMaxSheetNum: '5',
      fileType: 'EXCEL2007',
      async: 'false',
    };
  };

  return (
    <div
      id="incomingInspectionDashboard"
      className={`${styles.boardContainer} ${styles.dashboardContainer} ${isFullscreen ? styles.fullscreen : ''}`}
    >
      <header className={`${styles.header}`}>
        <div className={styles.headerLeft}>
          <div
            className={`${styles.datePickerWrapper}`}
          >
            <DatePicker
              className={`custom-date-picker ${styles.datePickerTransparent}`}
              popupCls="custom-datepicker-popup"
              placeholder="选择开始时间"
              value={startDate}
              onChange={date => setStartDate(date)}
              disabled={isDataLoading}
              clearButton={false}
              getPopupContainer={triggerNode => triggerNode.parentNode as HTMLElement}
            />
          </div>
          <div
            className={`${styles.datePickerWrapper}`}
          >
            <DatePicker
              className={`custom-date-picker ${styles.datePickerTransparent}`}
              popupCls="custom-datepicker-popup"
              placeholder="选择结束时间"
              value={endDate}
              onChange={date => setEndDate(date)}
              disabled={isDataLoading}
              clearButton={false}
              getPopupContainer={triggerNode => triggerNode.parentNode as HTMLElement}
            />
          </div>
          {isDataLoading && (
            <div className={styles.loadingContainer}>
              <div className={styles.loadingSpinner} />
              数据加载中...
            </div>
          )}
        </div>
        <div className={`${styles.headerCenter}`}>
          <div
            className={`${styles.title}`}
          />
          <div className={styles.enTitle}>DIGTAL VISUAL MONITORING SYSTEM</div>
        </div>
        <div className={styles.headerRight}>
          <div className={styles.timeWrapper}>
            <div className={styles.time}>{moment(formattedTime).format('HH:mm:ss')}</div>
            <div className={styles.date}>{moment(formattedTime).format('YYYY-MM-DD')}</div>
            <div className={styles.week}>{week}</div>
          </div>
          <div className={styles.fullscreenButton} onClick={toggleFullscreen}>
            <Icon type={isFullscreen ? "fullscreen_exit" : "fullscreen"} />
          </div>
        </div>
      </header>
      <main className={styles.mainContent}>
        <div className={`${styles.topRow}`}>
          <div className={styles.panel}>
            <div className={`${styles.panelHeader1}`}>
              <div className={`${styles.panelTitle}`}>来料检验进度图</div>
            </div>
            <div className={`${styles.panelBody} ${styles.chartPanelBody}`}>
              <div
                className={`${styles.pieChartBackground}`}
              />
              <div ref={pieChartRef} />
            </div>
          </div>
          <div className={styles.panel}>
            <div className={`${styles.panelHeader1} ${styles.panelHeaderCenterBg}`}>
              <div className={`${styles.panelTitle}`}>来料检验不良项目统计图</div>
            </div>
            <div className={`${styles.panelBody} ${styles.chartPanelBody}`}>
              <ECharts
                option={defectiveStatsOption}
                onEvents={{ click: onDefectiveChartClick }}
                forceFullWidth
                notMerge={false}
                lazyUpdate={false}
              />
            </div>
          </div>
          <div className={styles.panel}>
            <div className={`${styles.panelHeader1}`}>
              <div className={`${styles.panelTitle}`}>不良项目信息</div>
            </div>
            <div className={`${styles.panelBody}`}>
              <div className={styles.customTable}>
                <div
                  className={`${styles.tableHeader}`}
                >
                  <span>不良项目</span>
                  <span>检验单号</span>
                  <span>物料</span>
                  <span>供应商</span>
                  <span>下达时间</span>
                  <span>检验员</span>
                </div>
                <div className={styles.tableBody} ref={tableScrollRef}>
                  {(activeDefectiveItem
                    ? defectivePageData.filter(item => item.defectiveItem === activeDefectiveItem)
                    : defectivePageData
                  ).map(item => (
                    <div
                      className={`${styles.tableRow} ${item.isNewlyLoaded ? styles.entering : ''}`}
                      key={item.id}
                    >
                      <OverflowScroller>{item.defectiveItem}</OverflowScroller>
                      <OverflowScroller>
                        <span
                          className={`${styles.clickableLink}`}
                          onClick={() => handleInspectionIdClick(item.inspectionId, item.inspectDocId)}
                        >
                          {item.inspectionId}
                        </span>
                      </OverflowScroller>
                      <OverflowScroller>{item.material}</OverflowScroller>
                      <OverflowScroller>{item.supplier}</OverflowScroller>
                      <OverflowScroller>{item.creationDate}</OverflowScroller>
                      <OverflowScroller>{item.inspector}</OverflowScroller>
                    </div>
                  ))}
                  {defectivePageInfo.loading && (
                    <div className={styles.loadingRow}>
                      <span>加载中...</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className={`${styles.bottomRow}`}>
          <div className={`${styles.panel}`}>
            <div className={`${styles.panelHeader2}`}>
              <div className={`${styles.panelTitle}`}>来料检验-物料</div>
              <div className={styles.panelExtra}>
                <div
                  className={`${styles.assetButton}`}
                  onClick={() => {
                    setTempSelectedMaterial(null); // 先清空
                    setIsMaterialModalOpen(true);  // 再打开弹窗
                  }}
                >
                  物料筛选
                </div>
              </div>
            </div>
            <div className={`${styles.panelBody}`}>
              <div className={styles.customTable}>
                <div
                  className={`${styles.tableHeader2}`}
                >
                  <span>序号</span>
                  <span>物料</span>
                  <span>物料到货批</span>
                  <span>物料合格率</span>
                </div>
                <div className={styles.tableBody} ref={materialTableScrollRef}>
                  {materialPageData.map(item => (
                    <div
                      className={`${styles.tableRow2} ${item.isNewlyLoaded ? styles.entering : ''}`}
                      key={item.id}
                    >
                      <OverflowScroller>
                        <div
                          className={`${styles.tableIcon}`}
                        >
                          <span className={styles.tableIconContainer}>{item.id}</span>
                        </div>
                      </OverflowScroller>
                      <OverflowScroller>{item.material}</OverflowScroller>
                      <OverflowScroller >
                        {item.arrivalBatchCount}
                      </OverflowScroller>
                      <OverflowScroller >
                        {item.passRate}
                      </OverflowScroller>
                    </div>
                  ))}
                  {materialPageInfo.loading && (
                    <div className={styles.loadingRow}>
                      <span>加载中...</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
          <div className={`${styles.panel}`}>
            <div className={`${styles.panelHeader2}`}>
              <div className={`${styles.panelTitle}`}>来料检验-供应商</div>
              <div className={styles.panelExtra}>
                <div
                  className={`${styles.assetButton}`}
                  onClick={() => {
                    setTempSelectedSupplier(null); // 打开弹窗前清空勾选项
                    setIsSupplierModalOpen(true);
                  }}
                >
                  供应商筛选
                </div>
                <ExcelExport
                  requestUrl={`${BASIC.TARZAN_SAMPLING}/v1/${getCurrentOrganizationId()}/qms-inspect-iqc-dashboards/supplier-stats/export/ui`}
                  method="GET"
                  queryParams={getExportParams()}
                  buttonText="导出"
                  otherButtonProps={{
                    className: styles.exportBtn,
                    icon: undefined,
                  }}
                />
              </div>
            </div>
            <div className={`${styles.panelBody} ${styles.panelBodyNoPadding}`}>
              <div className={styles.customTable}>
                <div
                  className={`${styles.tableHeader2}`}
                >
                  <span>序号</span>
                  <span>供应商</span>
                  <span>供应商到货批</span>
                  <span>供应商合格率</span>
                </div>
                <div className={styles.tableBody} ref={supplierTableScrollRef}>
                  {supplierPageData.map(item => (
                    <div
                      className={`${styles.tableRow2} ${item?.isNewlyLoaded ? styles.entering : ''}`}
                      key={item?.id}
                    >
                      <OverflowScroller>
                        <div
                          className={`${styles.tableIcon}`}
                        >
                          <span className={styles.tableIconContainer}>{item.id}</span>
                        </div>
                      </OverflowScroller>
                      <OverflowScroller>{item.supplier}</OverflowScroller>
                      <OverflowScroller >
                        {item.arrivalBatchCount}
                      </OverflowScroller>
                      <OverflowScroller >
                        {item.passRate}
                      </OverflowScroller>
                    </div>
                  ))}
                  {supplierPageInfo.loading && (
                    <div className={styles.loadingRow}>
                      <span>加载中...</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      <MaterialFilterModal
        isOpen={isMaterialModalOpen}
        dataSet={materialLovDataSet}
        selectedRecord={tempSelectedMaterial}
        onSelect={handleMaterialSelect}
        onConfirm={handleMaterialConfirm}
        onCancel={handleMaterialCancel}
        onQuery={() => materialLovDataSet.query()}
      />

      <SupplierFilterModal
        isOpen={isSupplierModalOpen}
        dataSet={supplierLovDataSet}
        selectedRecord={tempSelectedSupplier}
        onSelect={handleSupplierSelect}
        onConfirm={handleSupplierConfirm}
        onCancel={handleSupplierCancel}
      />
    </div>
  );
};

export default IncomingInspectionDashboard;
