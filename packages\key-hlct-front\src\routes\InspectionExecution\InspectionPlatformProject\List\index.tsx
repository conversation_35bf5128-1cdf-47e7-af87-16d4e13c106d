/**
 * @Description: 检验记录平台-查询界面
 * @Author: <<EMAIL>>
 * @Date: 2023-02-10 17:18:09
 * @LastEditTime: 2023-05-18 17:03:16
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect, useState, useMemo, useRef } from 'react';
import moment from 'moment';
import { isNil } from 'lodash';
import { Table, DataSet, Modal, Spin, Lov, Form, Select, NumberField, TextField, DateTimePicker, Attachment, Currency, Tooltip as NewTooltip } from 'choerodon-ui/pro';
import { Badge, Popover, Tabs, Tag, } from 'choerodon-ui';
import uuid from 'uuid/v4';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { maxBy, isNumber, omit } from 'lodash';
import { Tooltip } from 'choerodon-ui/pro/lib/core/enum';
import { Button as PermissionButton } from 'components/Permission';
import { openTab } from 'utils/menuTab';
import { Header, Content } from 'components/Page';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import Record from 'choerodon-ui/pro/lib/data-set/Record';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import withProps from 'utils/withProps';
import ExcelExport from 'components/ExcelExport';
import { useDataSetEvent } from 'utils/hooks';
import notification from 'utils/notification';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { drawerPropsC7n } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import { BASIC, API_HOST } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import { TableDS, unclaimedTableDS, InspectMaterialDS, DistributeDS, InspectItemRowObjValueDS, InspectItemColDS } from '../stories';
import { AssignTask, RevokeTask, ResetTask, StartInspectInfo, SaveInspectInfo, SubmitInspectInfo, GetCalculateFormula, FormulaCalculation } from '../services';
import styles from './index.modules.less';
import NcRecordComponent from './NcRecordComponent';
import { ShowValidation } from 'choerodon-ui/pro/lib/form/enum';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.qms.inspectionPlatformProject';
const { TabPane } = Tabs;
const { ItemGroup, Item } = Form;
const { Option } = Select;
// 公示类检验项目提示数组
const formulaPromptArray = ['第一组结果值', '第二组结果值', '最终结果值', '精密度'];


const inspectionPlatformProjectList =
  props => {
    const {
      searchTableDS,
      pendingTableDS,
      unclaimedTableDs,
      history,
      customizeTable,
      customizeTabPane,
      inspectItemRowObjValueDS,
      inspectItemColDS
    } = props;

    enum TabKey {
      processTask = 'PENDING',
      receiveTask = 'UNCLAIMED',
    }

    enum ModeType {
      col = 'COL_MODE',
      row = 'ROW_MODE',
      rowObj = 'ROW_OBJ_MODE',
    }

    enum NcRecordDimension {
      taskNc = 'TASK_NC',
      itemNc = 'ITEM_NC',
    }

    enum FormulaDimension {
      sameObject = 'SAME_OBJECT',
      sameSequence = 'SAME_SERIAL_NUMBER',
      sameItem = 'SAME_ITEM',
    }

    // 检验条码明细
    const inspectMaterialDS = useMemo(() => new DataSet({ ...InspectMaterialDS() }), []);
    // 分配检验员
    const distributeDS = useMemo(() => new DataSet({ ...DistributeDS() }), []);

    const inspectItemRowDS = useMemo(() =>
      new DataSet({
        ...TableDS(),
        children: {
          taskLineObjects: inspectItemRowObjValueDS,
        },
      }), []);

    // tab键
    const [tabKey, setTabKey] = useState('processTask');
    // 待处理任务列表勾选
    const [pendingSelectList, setPendingSelectList] = useState([]);
    // 待领取任务列表勾选
    const [unclaimedSelectList, setUnclaimedSelectList] = useState([]);
    // 待处理总数量
    const [pendingCount, setPendingCount] = useState(0);
    // 待领取总数量
    const [unclaimedCount, setUnclaimedCount] = useState(0);
    // 带对象行模式动态宽度
    const [cacheMinWidth, setCacheMinWidth] = useState(180);
    // 检验项列模式主键值
    const [itemColKeyCount, setItemColKeyCount] = useState(1);
    // 存储新建检验项目行相关信息
    const [cacheAddLineItemInfo, setCacheAddLineItemInfo] = useState({});

    const hasExecutedRef = useRef(false);


    // 监听关系表格勾选数据
    const handlePendingTableSelect = ({ dataSet }) => {
      setPendingSelectList(dataSet.selected || []);
    };
    useDataSetEvent(pendingTableDS, 'select', handlePendingTableSelect);
    useDataSetEvent(pendingTableDS, 'selectAll', handlePendingTableSelect);
    useDataSetEvent(pendingTableDS, 'unselect', handlePendingTableSelect);
    useDataSetEvent(pendingTableDS, 'unselectAll', handlePendingTableSelect);

    const handleUnclaimedTableSelect = ({ dataSet }) => {
      setUnclaimedSelectList(dataSet.selected || []);
    };
    useDataSetEvent(unclaimedTableDs, 'select', handleUnclaimedTableSelect);
    useDataSetEvent(unclaimedTableDs, 'selectAll', handleUnclaimedTableSelect);
    useDataSetEvent(unclaimedTableDs, 'unselect', handleUnclaimedTableSelect);
    useDataSetEvent(unclaimedTableDs, 'unselectAll', handleUnclaimedTableSelect);

    // 监听查询
    const handleSearchTableQuery = async ({ dataSet }) => {
      await handleChangeActiveKey(dataSet.getQueryParameter('tableKey'));
      return false;
    };
    useDataSetEvent(searchTableDS, 'query', handleSearchTableQuery);

    useDataSetEvent(searchTableDS.queryDataSet, 'update', ({ name, record }) => {
      switch (name) {
        case 'siteObj':
          record.set('inspectBusinessTypeObj', null);
          break;
        default:
          break;
      }
    });

    // 获取计算公式所得值
    const { run: getCalculateFormula, loading: getCalculateFormulaLoading } = useRequest(
      GetCalculateFormula(),
      {
        manual: true,
        needPromise: true,
      },
    );
    // 获取计算公式所得值
    const { run: formulaCalculation, loading: formulaCalculationLoading } = useRequest(
      FormulaCalculation(),
      {
        manual: true,
        needPromise: true,
      },
    );

    // 领取/分配检验任务
    const { run: receiveTask, loading: receiveTaskLoading } = useRequest(AssignTask(), {
      manual: true,
      needPromise: true,
    });
    // 开始检验
    const { run: startInspectInfo, loading: startInspectInfoLoading } = useRequest(StartInspectInfo(), {
      manual: true,
      needPromise: true,
    });
    // 撤销检验任务
    const { run: revokeTask, loading: revokeTaskLoading } = useRequest(RevokeTask(), {
      manual: true,
      needPromise: true,
    });
    // 取消检验任务
    const { run: resetTask, loading: resetTaskLoading } = useRequest(ResetTask(), {
      manual: true,
      needPromise: true,
    });
    // 保存
    const { run: saveInspectInfo, loading: saveInspectInfoLoading } = useRequest(SaveInspectInfo(), {
      manual: true,
      needPromise: true,
    });
    // 提交
    const { run: submitInspectInfo, loading: submitInspectInfoLoading } = useRequest(SubmitInspectInfo(), {
      manual: true,
      needPromise: true,
    });

    useEffect(() => {
      pendingTableDS.setQueryParameter('tableKey', TabKey[tabKey]);
      searchTableDS.setQueryParameter('tableKey', tabKey);
      pendingTableDS.query().then(() => {
        setPendingCount(pendingTableDS.totalCount);
        onHandleRowData(pendingTableDS.toData());
      });
      unclaimedTableDs.setQueryParameter('tableKey', TabKey[tabKey]);
      unclaimedTableDs.query().then(() => {
        setUnclaimedCount(unclaimedTableDs.totalCount);
      });
    }, []);

    const onHandle = (dataSet) => {
      if (!hasExecutedRef.current) {
        hasExecutedRef.current = true;
        onHandleRowData(dataSet.toData())
      }
    }
    useDataSetEvent(pendingTableDS, 'load', ({ dataSet }) => onHandle(dataSet))

    const attachmentProps: any = {
      bucketName: 'qms',
      bucketDirectory: 'inspection-platform',
      accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*', 'video/*'],
      showValidation: ShowValidation.newLine,
      viewMode: 'popup',
    };

    const attachmentProps1: any = {
      name: 'otherEnclosure',
      bucketName: 'qms',
      labelLayout: 'float',
      bucketDirectory: 'inspection-platform',
      accept: ['.doc', '.ppt', '.docx', '.xlsx', '.xls', '.deb', '.txt', '.pdf', 'image/*', 'video/*'],
      showValidation: ShowValidation.newLine,
      viewMode: 'popup',
      readOnly: true,
    };

    // 行列模式-数值类型区间判断
    const onCheckValueArrayMethod = (checkList, value, trueFlag = false) => {
      let _checkFlag = false;
      checkList.forEach(item => {
        if (!_checkFlag) {
          if (item.indexOf(',') === -1) {
            // 单值
            _checkFlag = `${value}` === `${Number(item || 0)}`;
          } else {
            // 范围值
            let _valueRange = item;
            if (trueFlag) {
              // 符合值需要去掉标准值
              const _rangeArr = item.split('/');
              if (_rangeArr.length > 0) {
                _valueRange = _rangeArr[0];
              }
            }
            const _valueArr = _valueRange.substr(1, _valueRange.length - 2)?.split(',');
            if (_valueArr.length === 2) {
              const _leftChar = _valueRange.substr(0, 1);
              const _rightChar = _valueRange.substr(_valueRange.length - 1, 1);
              let _leftValue = _valueArr[0];
              let _rightValue = _valueArr[1];

              // 只有左侧在范围内才判断右边是否在
              let _leftFlag = true;
              const _reg = /^(-?\d+)(\.\d+)?$/;
              const newValue = Number(value || 0);
              if (_reg.test(_leftValue)) {
                _leftValue = Number(_leftValue || 0);
                if (_leftChar === '(') {
                  _leftFlag = _leftValue < newValue;
                } else {
                  _leftFlag = _leftValue <= newValue;
                }
              }
              if (_leftFlag) {
                if (_reg.test(_rightValue)) {
                  _rightValue = Number(_rightValue || 0);
                  if (_rightChar === ')') {
                    _checkFlag = _rightValue > newValue;
                  } else {
                    _checkFlag = _rightValue >= newValue;
                  }
                } else {
                  _checkFlag = true;
                }
              }
            }
          }
        }
      });
      return _checkFlag;
    };

    // 行列模式-录入值颜色判断
    const onGetValueColor = (value, dataType, falseValues, warningValues, trueValues) => {
      if (!value && value !== 0) {
        return null;
      }
      let _color = 'green';
      let _falseValueFlag = false;
      let _warningValueFlag = false;
      let _trueValueFlag = false;

      if ((falseValues || []).length > 0) {
        if (['VALUE', 'CALCULATE_FORMULA'].includes(dataType)) {
          _falseValueFlag = onCheckValueArrayMethod(falseValues, value);
        } else {
          falseValues.forEach(item => {
            if (!_falseValueFlag && `${item}` === `${value}`) {
              _falseValueFlag = true;
            }
          });
        }
      } else if ((trueValues || []).length > 0) {
        if (['VALUE', 'CALCULATE_FORMULA'].includes(dataType)) {
          _trueValueFlag = onCheckValueArrayMethod(trueValues, value, true);
        } else {
          trueValues.forEach(item => {
            if (!_trueValueFlag && `${item}` === `${value}`) {
              _trueValueFlag = true;
            }
          });
        }
      }
      if (
        !_falseValueFlag &&
        (warningValues || []).length > 0 &&
        ['VALUE', 'CALCULATE_FORMULA'].includes(dataType)
      ) {
        _warningValueFlag = onCheckValueArrayMethod(warningValues, value);
      }
      if (_falseValueFlag) {
        // 在不符合值中
        _color = 'red';
      } else if (_warningValueFlag) {
        // 在预警值中
        _color = 'yellow';
      }
      if (_warningValueFlag) {
        // 在预警值中
        _color = 'yellow';
      } else if ((falseValues || []).length > 0) {
        _color = _falseValueFlag ? 'red' : 'green';
      } else if ((trueValues || []).length > 0) {
        _color = _trueValueFlag ? 'green' : 'red';
      }
      return _color;
    };

    // 行模式DS添加检测值字段
    const onDSAddInspectValueField = (fieldName, targetDS, lineRequiredFlag, maxQty, minQty = 0) => {
      for (let i = minQty; i < maxQty; i++) {
        if (!targetDS.getField(`${fieldName}_VALUE${i}`)) {
          targetDS.addField(`${fieldName}_VALUE${i}`, {
            name: `${fieldName}_VALUE${i}`,
            type: FieldType.string,
            label: `${intl.get(`${modelPrompt}.model.line.inspectValue`).d('检测值')}`,
            dynamicProps: {
              required: ({ record, dataSet }) => {
                const _disabled = !!record?.get(`${fieldName}_DISABLED`);
                let _objRequiredFlag = true;
                const _modeType = pendingTableDS.getState('modeType');
                if (_modeType === ModeType.rowObj) {
                  _objRequiredFlag = record?.get('inspectObjectId');
                }
                return (
                  record &&
                  !['UNQUALIFIED_INSPECTION_TASK', 'UNQUALIFIED_ITEMS'].includes(
                    dataSet.getState('resultDimension'),
                  ) &&
                  !_disabled &&
                  record.get('fieldName') === fieldName &&
                  lineRequiredFlag === 'Y' &&
                  _objRequiredFlag &&
                  record.get('dataType') !== 'CALCULATE_FORMULA'
                );
              },
            },
          });
        }
      }
    };

    // 行模式(不带检验对象)-处理数据
    const onHandleRowData = docLines => {
      inspectItemRowDS.setState('modeType', ModeType.row);
      // const _resultDimension = inspectInfoDS.current?.get('resultDimension');

      let _ngQty = 0;
      let _okQty = 0;
      let _maxSamplingQty = 0;

      // 计算公式关联检验项行
      // ITEM_ID_检验项ID -> 影响检验项ID数组
      const _formulaObj: any = {};
      // 检验项ID -> fieldName
      const _itemIdFieldNameObj: any = {};
      // 抽样数量是否存在可编辑
      let _userDefinedSamplingFlag = false;
      // 临时存储计算公式关系map
      const taskItemMap = new Map();

      // 自设主键用于动态录入值
      let _itemColKeyCount = 1;
      const newDocLines = docLines.map(lineItem => {
        const _fieldName = `ITEM${_itemColKeyCount}`;
        _itemColKeyCount++;

        const _dataType = lineItem.dataType;

        let _samplingQty = 0;
        if (lineItem?.dataQtyDisposition === 'DATA') {
          _samplingQty = Number(lineItem.dataQty || 0);
        } else if (lineItem?.dataQtyDisposition === 'SAMPLE') {
          _samplingQty = Number(lineItem.samplingQty || 0);
        } else {
          _samplingQty = Number(lineItem.samplingQty || 0) * Number(lineItem.dataQty || 0);
        }

        const _falseValues = lineItem.falseValues || [];
        const _warningValues = lineItem.warningValues || [];
        const _trueValues = lineItem.trueValues || [];
        const _requiredFlag = lineItem.requiredFlag;
        lineItem.samplingQtyCount = _samplingQty;
        _maxSamplingQty = _samplingQty > _maxSamplingQty ? _samplingQty : _maxSamplingQty;

        const _inspectResult = lineItem.inspectResult;
        if (_inspectResult === 'NG') {
          _ngQty++;
        } else if (_inspectResult === 'OK') {
          _okQty++;
        }

        if (_dataType === 'CALCULATE_FORMULA') {
          const _currentMapItem = taskItemMap.get(lineItem.inspectDocLineId) || [];
          (lineItem.formulaObj?.formulaList || []).forEach(formulaItem => {
            if (formulaItem.inspectItemId) {
              const _targetInspectItemIds = _formulaObj[lineItem.inspectDocLineId] || [];
              if (!_targetInspectItemIds.includes(lineItem.inspectDocLineId)) {
                _targetInspectItemIds.push(formulaItem.inspectDocLineId);
                _formulaObj[lineItem.inspectDocLineId] = _targetInspectItemIds;
              }
            }
            _currentMapItem.push(formulaItem.inspectDocLineId);
          });
          taskItemMap.set(lineItem.inspectDocLineId, _currentMapItem);
        }
        _itemIdFieldNameObj[`ITEM_ID_${lineItem.inspectDocLineId}`] = _fieldName;

        // 存储录入值原始数据
        const _taskLineActDtlObj: any = {};
        let _maxSeq = 0;

        // DS添加检测值字段
        onDSAddInspectValueField(_fieldName, inspectItemRowDS, _requiredFlag, _samplingQty);

        // 拼接原始检验记录
        let _sourceInspectValue = '';
        (lineItem.sourceValues || []).forEach(sourceItem => {
          const { sourceInspectValue } = sourceItem;
          if (sourceInspectValue) {
            _sourceInspectValue += `${sourceInspectValue},`;
          }
        });
        if (_sourceInspectValue.length > 0) {
          _sourceInspectValue = _sourceInspectValue.substring(0, _sourceInspectValue.length - 1);
        }

        let _index = 0;

        // 计算公式数量
        let _formulaCount = 0;
        (lineItem.taskLineObjects || []).forEach(objItem => {
          (objItem.taskLineActDtls || []).forEach(dtlItem => {
            // 处理录入值
            lineItem[`${_fieldName}_VALUE${_index}`] = dtlItem.inspectValue;
            lineItem[`${_fieldName}_VALUE${_index}_ID`] = dtlItem.inspectDocLineActDtlId;
            _taskLineActDtlObj[dtlItem.inspectDocLineActDtlId] = dtlItem;
            _maxSeq =
              _maxSeq > Number(dtlItem.objectEnterValueSequence || 0)
                ? _maxSeq
                : Number(dtlItem.objectEnterValueSequence || 0);

            // 查询数据颜色处理
            if (['VALUE', 'DECISION_VALUE', 'VALUE_LIST', 'CALCULATE_FORMULA'].includes(_dataType)) {
              lineItem[`${_fieldName}_VALUE${_index}_COLOR`] = onGetValueColor(
                dtlItem.inspectValue,
                _dataType,
                _falseValues,
                _warningValues,
                _trueValues,
              );
            }
            _index++;

            // 计算公式计数
            if (_dataType === 'CALCULATE_FORMULA') {
              _formulaCount++;
            }
          });
        });

        // 计算公式数量赋值
        if (_dataType === 'CALCULATE_FORMULA') {
          lineItem.formulaCount = _formulaCount;
        }

        // 抽样数量是否存在可编辑
        if (!_userDefinedSamplingFlag && lineItem.samplingType === 'USER_DEFINED_SAMPLING') {
          _userDefinedSamplingFlag = true;
        }

        _maxSeq++;
        return {
          ...lineItem,
          sourceInspectValue: _sourceInspectValue,
          fieldName: _fieldName,
          cacheMaxSeq: _maxSeq,
          cacheTaskLineActDtlObj: _taskLineActDtlObj,
          maxSamplingQty: lineItem.samplingQty,
        };
      });

      // 计算列宽
      if (_maxSamplingQty > 2) {
        inspectItemRowDS.setState('maxSamplingQty', _maxSamplingQty);
        let _newCacheMinWidth = _maxSamplingQty * 65 + (_maxSamplingQty - 1) * 5 + 16;
        setCacheMinWidth(_newCacheMinWidth);

      }

      setItemColKeyCount(_itemColKeyCount);
      const targetInspectItemIds: any = [];
      taskItemMap.forEach((item: number[], key: number) => {
        targetInspectItemIds.push([key, ...item]);
      });
      taskItemMap.clear();
      pendingTableDS.setState('targetInspectItemIds', targetInspectItemIds)
      pendingTableDS.loadData(newDocLines);

      // 设置必输需要的判定字段
      // inspectItemRowDS.setState('resultDimension', _resultDimension);
      inspectItemRowDS.setState('cacheFormulaObj', {
        formulaObj: _formulaObj,
        itemIdFieldNameObj: _itemIdFieldNameObj,
        userDefinedSamplingFlag: _userDefinedSamplingFlag ? 'Y' : 'N',
      });

      // 计算任务结果值
      // if (
      //   !inspectInfoDS.current?.get('inspectResult') &&
      //   ['INSPECTING'].includes(inspectInfoDS.current?.get('inspectTaskStatus'))
      // ) {
      //   inspectInfoDS.current?.init('inspectResult', _ngQty > 0 ? 'NG' : _okQty > 0 ? 'OK' : null);
      // }

      // 计算合格数和不合格数
      // inspectItemRowDS.records.forEach(record => {
      //   const { okQty, ngQty, samplingQty } = record.data;
      //   const _newOkQty =
      //     okQty || okQty === 0 ? okQty : Number(samplingQty || 0) - Number(ngQty || 0);
      //   record.init('okQty', _newOkQty);
      //   record.init(
      //     'ngQty',
      //     ngQty || ngQty === 0 ? ngQty : Number(samplingQty || 0) - Number(_newOkQty || 0),
      //   );
      // });
      // };
      //  数据加载完成
      hasExecutedRef.current = false;
    }

    // 行列模式-检验行判定变化
    const handleChangeInspectResult = (newValue, oldValue, record) => {
      const _modeType = inspectItemRowDS.getState('modeType');
      if (newValue !== oldValue) {
        if (_modeType === ModeType.col) {
          // // 列模式需要计算不合格数
          // let _ngQty = 0;
          // let _okQty = 0;
          // const _data = record.data || {};
          // Object.keys(_data).forEach(key => {
          //   if (key.indexOf('_RESULT') !== -1) {
          //     if (_data[key] === 'NG') {
          //       _ngQty++;
          //     } else if (_data[key] === 'OK') {
          //       _okQty++;
          //     }
          //   }
          // });
          // inspectInfoDS.current?.set('inspectResult', _ngQty > 0 ? 'NG' : _okQty > 0 ? 'OK' : null);
        } else if (newValue === 'NG') {
          inspectItemRowDS.current?.set('inspectResult', 'NG');
        } else {
          const ngRecordFlag = inspectItemRowDS.records.find(
            item => item.get('inspectResult') === 'NG',
          );
          if (ngRecordFlag) {
            inspectItemRowDS.current?.set('inspectResult', 'NG');
          } else {
            const okRecordFlag = inspectItemRowDS.records.find(
              item => item.get('inspectResult') === 'OK',
            );
            inspectItemRowDS.current?.set('inspectResult', okRecordFlag ? 'OK' : null);
          }
        }
        pendingTableDS.setState('docChangeFlag', 'N');
      }
    };

    // 行列模式-录入值合格数不合格数计算及交互
    const handleComputedQty = (
      fieldName, // 列唯一键
      dataType, // 检验项类型
      samplingFlag = false, // 抽样数小于已录入行数时，是否清空行
      resultFlag = true, // 检验项结果是否根据合格数和不合格数计算
      lineRecord, // 当前行
      formulaFlag = true, // 触发计算公式
      valueName = '', // 不带对象的行模式当前录入框
      delInspectObjectId = null, // 用于清空检验对象触发时的处理
    ) => {
      let _lineRecord: Record = lineRecord || pendingTableDS.current;

      const _modeType = inspectItemRowDS.getState('modeType');
      let _ngQty = 0;
      let _dataQty = 0;
      let _samplingQty = 0;
      let _samplingQtyCount = 0;
      let _samplingType;
      let _dataType = '';
      let _targetRecords: Array<any> = [];
      let _acceptStandard;
      let _requiredFlag = 'N';
      let _curInspectItemId;
      let _cacheItemData: any = {};
      let _cacheInfo: any = {};

      if (_modeType === ModeType.col) {
        // _cacheItemData = inspectItemColDS.getState('cacheItemData') || {};
        // _cacheInfo = _cacheItemData ? _cacheItemData[fieldName] : {};

        // if (
        //   _cacheInfo?.dataQtyDisposition === 'DATA' ||
        //   _cacheInfo?.dataQtyDisposition === 'SAMPLE'
        // ) {
        //   _dataQty = 1;
        //   _samplingQty = 1;
        // } else {
        //   _dataQty = Number(_cacheInfo?.dataQty || 0);
        //   _samplingQty = Number(_cacheInfo?.newSamplingQty || 0);
        // }

        // _samplingType = _cacheInfo?.samplingType;
        // _targetRecords = inspectItemColDS.records || [];
        // _acceptStandard = _cacheInfo?.acceptStandard;
        // _curInspectItemId = _cacheInfo?.inspectItemId;
        // _dataType = _cacheInfo?.dataType;
        // _lineRecord = inspectItemColDS.current;
      } else {
        if (_cacheInfo?.dataQtyDisposition === 'DATA') {
          _dataQty = Number(_lineRecord?.get('dataQty') || 0);
          _samplingQty = 1;
        } else if (_cacheInfo?.dataQtyDisposition === 'SAMPLE') {
          _dataQty = 1;
          _samplingQty = Number(_lineRecord?.get('samplingQty') || 0);
        } else {
          _dataQty = Number(_lineRecord?.get('dataQty') || 0);
          _samplingQty = Number(_lineRecord?.get('samplingQty') || 0);
        }

        if (_modeType === ModeType.row) {
          _dataQty += Number(_lineRecord?.get('addQtyCount') || 0);
        }

        _samplingType = _lineRecord?.get('samplingType');
        _dataType = _lineRecord?.get('dataType');
        _acceptStandard = _lineRecord?.get('acceptStandard');
        _requiredFlag = _lineRecord?.get('requiredFlag');
        _curInspectItemId = _lineRecord?.get('inspectDocLineId');
        if (_modeType === ModeType.rowObj) {
          _targetRecords = _lineRecord?.getCascadeRecords('taskLineObjects') || [];
          _samplingQtyCount = _samplingQty;
        } else {
          _samplingQtyCount = Number(_lineRecord?.get('samplingQtyCount') || 0);
        }
        _samplingQtyCount += Number(_lineRecord?.get('addQtyCount') || 0);
      }

      if (_modeType !== ModeType.row) {
        // 获取当前检验项不同行的所有录入值：对象->值
        const _dataObjInfo: any = {};
        let _dataObjInfoCount = 0;
        const _objRecords: Array<any> = [];
        let _inspectValueRecord = '';
        _targetRecords.forEach(record => {
          if (_modeType === ModeType.rowObj) {
            // 判断对应的输入框个数
            if (record?.get('taskLineActDtls')?.length > 0) {
              if (_dataQty < record?.get('taskLineActDtls')?.length) {
                _dataQty = record?.get('taskLineActDtls')?.length;
              }
            }
            _dataQty += Number(record?.get('addQtyCount') || 0);
            _dataQty = _dataQty === 0 ? 1 : _dataQty;
          } else {
            const initLength = record.get(`${fieldName}_taskLineActDtlsLength`) || 0;
            const _addQtyCount = Number(record.get(`${fieldName}_addQtyCount`) || 0);

            if (_dataQty < initLength) {
              _dataQty = initLength;
            }

            _dataQty += _addQtyCount;
          }
          if (record.get('cacheInspectObjectId') && record.status !== 'delete') {
            _objRecords.push(record);
            const _dataObjList: Array<any> = [];
            // 不合格标识
            let _ngFlag = false;
            for (let i = 0; i < _dataQty; i++) {
              const _value = record?.get(`${fieldName}_VALUE${i}`);
              if (_value || _value === 0) {
                _dataObjList.push(_value);
                if (_modeType === ModeType.rowObj) {
                  if (_dataType === 'DATE') {
                    _inspectValueRecord += `${moment(_value).format('YYYY-MM-DD HH:mm:ss')},`;
                  } else {
                    _inspectValueRecord += `${_value},`;
                  }
                }
              }
              if (!_ngFlag && record?.get(`${fieldName}_VALUE${i}_COLOR`) === 'red') {
                _ngFlag = true;
              }
            }
            if (_dataObjList.length > 0) {
              _dataObjInfo[record.get('cacheInspectObjectId')] = _dataObjList;
              _dataObjInfoCount++;
            }
            if (_ngFlag) {
              _ngQty++;
            }
          }
        });

        // 设置行模式录入记录
        if (_inspectValueRecord.length > 0) {
          _inspectValueRecord = _inspectValueRecord.substring(0, _inspectValueRecord.length - 1);
          _lineRecord?.init('inspectValueRecord', _inspectValueRecord);
        } else {
          _lineRecord?.init('inspectValueRecord', null);
        }

        // 自定义抽样时设置抽样数
        if (_samplingType === 'USER_DEFINED_SAMPLING') {
          // 自定义抽样同步抽样数
          if (_modeType === ModeType.col) {
            _samplingQty = _dataObjInfoCount;
            const _recordSamplingQty = inspectItemColDS.find(
              record => record.get('inspectItemKey') === 'samplingQty',
            );
            _recordSamplingQty?.set(fieldName, _samplingQty);
            _cacheItemData[fieldName] = {
              ..._cacheInfo,
              newSamplingQty: _samplingQty,
            };
            inspectItemColDS.setState('cacheItemData', _cacheItemData);
          }
        }

        if (_dataType !== 'CALCULATE_FORMULA' && _samplingType !== 'USER_DEFINED_SAMPLING') {
          // 录入值的行数与抽样数相等时其他行不可输入
          if (_dataObjInfoCount === _samplingQty) {
            _objRecords.forEach(record => {
              record.init(
                `${fieldName}_DISABLED`,
                (_dataObjInfo[record.get('cacheInspectObjectId')] || []).length < 1,
              );
            });
          } else {
            _targetRecords.forEach(record => {
              if (_modeType === ModeType.rowObj) {
                // 判断对应的输入框个数
                if (record?.get('taskLineActDtls')?.length > 0) {
                  if (_dataQty < record?.get('taskLineActDtls')?.length) {
                    _dataQty = record?.get('taskLineActDtls')?.length;
                  }
                }
                _dataQty += Number(record?.get('addQtyCount') || 0);
              }
              if (record.get('cacheInspectObjectId')) {
                record.init(`${fieldName}_DISABLED`, false);
                if (samplingFlag && _dataObjInfoCount > _samplingQty) {
                  // 判断是否清空的数据中存在不符合值
                  let _itemNgFlag = false;
                  for (let i = 0; i < _dataQty; i++) {
                    if (record.get(`${fieldName}_VALUE${i}`)) {
                      if (!_itemNgFlag) {
                        const _color = record.get(`${fieldName}_VALUE${i}_COLOR`);
                        if (_color === 'red') {
                          _itemNgFlag = true;
                        }
                      }
                      record.set(`${fieldName}_VALUE${i}`, null);
                      record.set(`${fieldName}_VALUE${i}_COLOR`, null);
                    }
                  }
                  if (_itemNgFlag) {
                    _ngQty--;
                  }
                }
              }
            });
          }
        }
      } else if (samplingFlag) {
        // 行模式抽样数量变化
        const _maxSamplingQty = Number(_lineRecord?.get('maxSamplingQty') || 0);
        if (_maxSamplingQty < _samplingQtyCount) {
          // 当抽样数变大时需要新加DS字段
          onDSAddInspectValueField(
            fieldName,
            inspectItemRowDS,
            _requiredFlag,
            _samplingQtyCount,
            _maxSamplingQty,
          );
          _ngQty = Number(_lineRecord?.get('ngQty') || 0);
          _lineRecord?.set('maxSamplingQty', _samplingQtyCount);
        } else {
          // 当抽样数变小时需要清空多余数据
          for (let i = _samplingQtyCount; i < _maxSamplingQty; i++) {
            _lineRecord.set(`${fieldName}_VALUE${i}`, null);
            _lineRecord.set(`${fieldName}_VALUE${i}_COLOR`, null);
          }
          if (['VALUE', 'DECISION_VALUE', 'VALUE_LIST', 'CALCULATE_FORMULA'].includes(dataType)) {
            for (let i = 0; i < _samplingQtyCount; i++) {
              if (_lineRecord.get(`${fieldName}_VALUE${i}_COLOR`) === 'red') {
                _ngQty++;
              }
            }
          }
        }
      } else if (['VALUE', 'DECISION_VALUE', 'VALUE_LIST', 'CALCULATE_FORMULA'].includes(dataType)) {
        for (let i = 0; i < _samplingQtyCount; i++) {
          if (_lineRecord.get(`${fieldName}_VALUE${i}_COLOR`) === 'red') {
            _ngQty++;
          }
        }
      }


      if (resultFlag) {
        // 解析RE值
        let _re = 1;
        if (_acceptStandard) {
          const _acAndReArr = _acceptStandard.split('/');
          if (_acAndReArr.length > 1) {
            _re = _acAndReArr[1];
          }
        }

        let _oldInspectResult;
        let _newInspectResult;
        let _inspectResultRecord;
        // 合格数不合格数和检验结果变化
        if (_modeType === ModeType.col) {
          const _record = inspectItemColDS.find(record => record.get('inspectItemKey') === 'okAndNg');
          _record?.init(`${fieldName}_NG`, _ngQty);
          _record?.init(`${fieldName}_OK`, _samplingQty - _ngQty < 0 ? 0 : _samplingQty - _ngQty);
          const _recordResult = inspectItemColDS.find(
            record => record.get('inspectItemKey') === 'inspectResult',
          );
          _oldInspectResult = _recordResult?.get(`${fieldName}_RESULT`);
          _newInspectResult = _re ? (_ngQty >= Number(_re || 1) ? 'NG' : 'OK') : 'OK';
          _inspectResultRecord = _recordResult;
          _recordResult?.set(`${fieldName}_RESULT`, _newInspectResult);
        } else {
          _lineRecord?.set('ngQty', _ngQty);
          _lineRecord?.set('okQty', _samplingQtyCount - _ngQty < 0 ? 0 : _samplingQtyCount - _ngQty);
          _oldInspectResult = _lineRecord?.get('inspectResult');
          _newInspectResult = _re ? (_ngQty >= Number(_re || 1) ? 'NG' : 'OK') : 'OK';
          _inspectResultRecord = _lineRecord;
          _lineRecord?.set('inspectResult', _newInspectResult);
        }
        handleChangeInspectResult(_newInspectResult, _oldInspectResult, _inspectResultRecord);
      }

      if (formulaFlag && dataType !== 'CALCULATE_FORMULA') {
        onComputedCalculateFormula(
          _modeType,
          _curInspectItemId,
          _modeType === ModeType.rowObj ? inspectItemRowObjValueDS.current : _lineRecord,
          valueName,
          delInspectObjectId,
        );
      }
    };

    // 计算公式数据添加
    const onForArrayAddValue = (maxQty, record, fieldName, list) => {
      let _valueFlag = false;
      for (let i = 0; i < maxQty; i++) {
        const _value = record.get(`${fieldName}_VALUE${i}`);
        list.push(_value);
        if (!_valueFlag && (_value || _value === 0)) {
          _valueFlag = true;
        }
      }
      return _valueFlag;
    };

    // 行列模式-计算公式
    const onComputedCalculateFormula = async (
      modeType,
      curInspectItemId,
      curRecord,
      valueName,
      delInspectObjectId = null,
    ) => {
      // 缓存的计算公式相关数据
      const _cacheFormulaObj = inspectItemRowDS.getState('cacheFormulaObj') || {};
      const _formulaObj = _cacheFormulaObj.formulaObj || {};
      const _itemIdFieldNameObj = _cacheFormulaObj.itemIdFieldNameObj || {};

      // 列模式缓存项信息
      const _cacheItemData = inspectItemColDS?.getState('cacheItemData') || {};

      // 调用计算公式接口的传参
      const _params: Array<any> = [];

      // 受该检验项影响的项ID集合
      const findFormulaItemIds: any = Object.entries(_formulaObj).find((item: any) => (item[1] || []).includes(curInspectItemId));
      const _targetInspectItemIds: number[] = [];
      if (!findFormulaItemIds) {
        return false;
      }
      _targetInspectItemIds.push(Number(findFormulaItemIds[0]));


      // 行模式计算公式项ID -> Record
      const _calculateFormulaRecordInfo: any = {};
      // 列模式存在按项目计算时获取所有录入值所在行
      let _cacheInspectObjectList: Array<any> = [];

      // 检验项列模式删除检验对象或新增检验项的缓存数据
      const _itemColUpdateCacheObj = pendingTableDS.getState('itemColUpdateCacheObj') || {};
      const _inspectTaskObjectActIds = _itemColUpdateCacheObj?.deleteActIds || [];
      const _inspectDocLineActDtlIds = _itemColUpdateCacheObj?.deleteDtlIds || [];
      // 列模式缓存dtl信息
      const _itemColDtlObj = inspectItemColDS?.getState('itemColDtlObj') || {};

      _targetInspectItemIds.forEach(formulaItemId => {
        let _formulaItemRecord: any;
        // 获取受影响的计算公式类型检验项的公式信息
        let _formulaInfo: any;
        if (modeType === ModeType.col) {
          const _fieldName = _itemIdFieldNameObj[`ITEM_ID_${formulaItemId}`];
          const _cacheItemInfo = _cacheItemData[_fieldName] || {};
          _formulaInfo = _cacheItemInfo.formulaObj || {};
          // 项目相关信息
          _formulaInfo.decimalNumber = _cacheItemInfo.decimalNumber;
          _formulaInfo.processMode = _cacheItemInfo.processMode;
          _formulaInfo.dataQty = Number(_cacheItemInfo.dataQty || 0);
          _formulaInfo.fieldName = _cacheItemInfo.fieldName;
        } else {
          _formulaItemRecord = pendingTableDS.find(
            record => record.get('inspectDocLineId') === formulaItemId,
          );
          _formulaInfo = _formulaItemRecord?.get('formulaObj') || {};
          // 项目相关信息
          _formulaInfo.decimalNumber = _formulaItemRecord?.get('decimalNumber');
          _formulaInfo.processMode = _formulaItemRecord?.get('processMode');
          _formulaInfo.dataQty = Number(_formulaItemRecord?.get('dataQty') || 0);
          _formulaInfo.fieldName = _formulaItemRecord?.get('fieldName');
        }
        const {
          formulaCode,
          dimension,
          decimalNumber,
          processMode,
          formulaList,
          fieldName,
        } = _formulaInfo;
        if (
          _formulaInfo?.formulaCode &&
          (formulaList || []).length > 0 &&
          (modeType !== ModeType.row || dimension !== FormulaDimension.sameObject)
        ) {
          // 与公式相关的录入值传参
          const _formulaList: Array<any> = [];

          if (_cacheInspectObjectList.length < 1 && dimension === FormulaDimension.sameItem) {
            _cacheInspectObjectList = inspectItemColDS.records.filter(
              record => record.get('cacheInspectObjectId') && record.status !== 'delete',
            );
          }
          const _inspectObjectId = curRecord.get('inspectObjectId') || delInspectObjectId;
          const _sequence = curRecord.get('sequence');

          // 存在不为空的值的标识
          let _addFlag = false;
          formulaList.forEach(formulaItem => {
            const { inspectItemId, isRequired, fieldCode, inspectDocLineId } = formulaItem;
            if (!inspectItemId) {
              _formulaList.push(formulaItem);
            } else {
              const _inspectValue: Array<any> = [];

              if (modeType === ModeType.col) {
                // 如果为列模式并且为按对象或序号则直接取当前行的数据
                const _fieldName = _itemIdFieldNameObj[`ITEM_ID_${inspectDocLineId}`];
                const _cacheItemInfo = _cacheItemData[_fieldName] || {};
                const _dataQty = Number(_cacheItemInfo.dataQty || 0);
                // 根据计算维护获取录入值
                let _valueFlag = false;
                if (
                  (dimension === FormulaDimension.sameObject && _inspectObjectId) ||
                  (dimension === FormulaDimension.sameSequence && _sequence)
                ) {
                  // 按相同对象或相同序号的维护
                  _valueFlag = onForArrayAddValue(_dataQty, curRecord, _fieldName, _inspectValue);
                } else if (dimension === FormulaDimension.sameItem) {
                  // 需要获取项下所有值
                  _cacheInspectObjectList.forEach(_objRecord => {
                    const _objValueFlag = onForArrayAddValue(
                      _dataQty,
                      _objRecord,
                      _fieldName,
                      _inspectValue,
                    );
                    if (!_valueFlag) {
                      _valueFlag = _objValueFlag;
                    }
                  });
                }
                if (_valueFlag) {
                  _addFlag = true;
                }
                _formulaList.push({
                  inspectItemId,
                  isRequired,
                  fieldCode,
                  inspectObjectId:
                    dimension === FormulaDimension.sameObject ? _inspectObjectId : null,
                  objectInspectSequence: dimension === FormulaDimension.sameItem ? null : _sequence,
                  inspectValue: _inspectValue,
                });
              } else if (modeType === ModeType.rowObj) {
                // 根据计算维护获取录入值
                const _lineFormulaItemRecord = inspectItemRowDS.find(
                  record => record.get('inspectItemId') === inspectItemId,
                );
                const _objRecords =
                  _lineFormulaItemRecord?.getCascadeRecords('taskLineObjects') || [];
                if (_objRecords.length > 0 && _lineFormulaItemRecord) {
                  const _dataQty = Number(_lineFormulaItemRecord.get('dataQty') || 0);
                  const _fieldName = _lineFormulaItemRecord.get('fieldName');
                  let _valueFlag = false;
                  if (
                    (dimension === FormulaDimension.sameObject && _inspectObjectId) ||
                    (dimension === FormulaDimension.sameSequence && _sequence)
                  ) {
                    // 按相同对象或相同序号的维护
                    const _objRecord = _objRecords.find(record =>
                      dimension === FormulaDimension.sameObject
                        ? record.get('inspectObjectId') === _inspectObjectId
                        : record.get('sequence') === _sequence,
                    );
                    if (_objRecord) {
                      _valueFlag = onForArrayAddValue(
                        _dataQty,
                        _objRecord,
                        _fieldName,
                        _inspectValue,
                      );
                    }
                  } else if (dimension === FormulaDimension.sameItem) {
                    // 需要获取项下所有值
                    _objRecords.forEach(_objRecord => {
                      const _objValueFlag = onForArrayAddValue(
                        _dataQty,
                        _objRecord,
                        _fieldName,
                        _inspectValue,
                      );
                      if (!_valueFlag) {
                        _valueFlag = _objValueFlag;
                      }
                    });
                  }
                  if (_valueFlag) {
                    _addFlag = true;
                  }
                  _formulaList.push({
                    inspectItemId,
                    isRequired,
                    fieldCode,
                    inspectObjectId:
                      dimension === FormulaDimension.sameObject ? _inspectObjectId : null,
                    objectInspectSequence: dimension === FormulaDimension.sameItem ? null : _sequence,
                    inspectValue: _inspectValue,
                  });
                }
              } else if (modeType === ModeType.row) {
                // 根据计算维护获取录入值
                const _lineFormulaItemRecord = pendingTableDS.find(
                  record => record.get('inspectDocLineId') === inspectDocLineId,
                );
                if (_lineFormulaItemRecord) {
                  const _fieldName = _lineFormulaItemRecord.get('fieldName');
                  if (dimension === FormulaDimension.sameSequence) {
                    const _sequence = Number(valueName.split('_VALUE')[1] || 0);
                    const _value = _lineFormulaItemRecord.get(`${_fieldName}_VALUE${_sequence}`);
                    if (_value || _value === 0) {
                      _inspectValue.push(_value);
                      _addFlag = true;
                      _formulaList.push({
                        inspectItemId,
                        inspectDocLineId,
                        isRequired,
                        fieldCode,
                        inspectObjectId: null,
                        objectInspectSequence: _sequence + 1,
                        inspectValue: _inspectValue,
                      });
                    }
                  } else if (dimension === FormulaDimension.sameItem) {
                    let _valueFlag = false;
                    const _samplingQtyCount = Number(
                      _lineFormulaItemRecord.get('samplingQtyCount') || 0,
                    );
                    _valueFlag = onForArrayAddValue(
                      _samplingQtyCount,
                      _lineFormulaItemRecord,
                      _fieldName,
                      _inspectValue,
                    );
                    if (_valueFlag) {
                      _addFlag = true;
                    }
                    _formulaList.push({
                      inspectItemId,
                      isRequired,
                      fieldCode,
                      inspectObjectId: null,
                      objectInspectSequence: null,
                      inspectValue: _inspectValue,
                    });
                  }
                }
              }
            }
          });

          // 封装传参，未传参的数据需清空对应检验项计算结果
          if (_addFlag) {
            _params.push({
              formulaItemId,
              formulaCode,
              dimension,
              decimalNumber,
              processMode,
              formulaList: _formulaList,
            });
            _calculateFormulaRecordInfo[`ITEM_ID_${formulaItemId}`] = _formulaItemRecord;
          } else if (modeType === ModeType.col) {
            const _cacheInspectObjectId = curRecord.get('cacheInspectObjectId');
            const _formulaCount = Number(curRecord.get(`${fieldName}_FORMULA_COUNT`) || 0);
            // 后台删除act和dtl的Id
            for (let i = 0; i < _formulaCount; i++) {
              const _itemColKey = `KEY${_cacheInspectObjectId}_${fieldName}_VALUE${i}`;
              const _cacheDtlInfo = _itemColDtlObj[_itemColKey] || {};
              const _dtlId = _cacheDtlInfo?.inspectDocLineActDtlId;
              if (_dtlId && !_inspectDocLineActDtlIds.includes(_dtlId)) {
                _inspectDocLineActDtlIds.push(_dtlId);
                _itemColDtlObj[_itemColKey] = {};
              }
            }
            if ([FormulaDimension.sameObject, FormulaDimension.sameSequence].includes(dimension)) {
              for (let i = 0; i < _formulaCount; i++) {
                curRecord.set(`${fieldName}_VALUE${i}`, null);
                curRecord.set(`${fieldName}_VALUE${i}_COLOR`, null);
              }
              curRecord.set(`${fieldName}_FORMULA_COUNT`, 0);
            } else if (dimension === FormulaDimension.sameItem) {
              _cacheInspectObjectList.forEach(_objRecord => {
                const _formulaCount = Number(_objRecord.get(`${fieldName}_FORMULA_COUNT`) || 0);
                for (let i = 0; i < _formulaCount; i++) {
                  _objRecord.set(`${fieldName}_VALUE${i}`, null);
                  _objRecord.set(`${fieldName}_VALUE${i}_COLOR`, null);
                }
                _objRecord.set(`${fieldName}_FORMULA_COUNT`, 0);
              });
            }
          } else if (modeType === ModeType.rowObj) {
            const _taskLineObjects = _formulaItemRecord.get('taskLineObjects') || [];
            if (_taskLineObjects.length > 0) {
              if (
                (dimension === FormulaDimension.sameObject && _inspectObjectId) ||
                (dimension === FormulaDimension.sameSequence && _sequence)
              ) {
                const _taskLineObject = _taskLineObjects.find(record =>
                  dimension === FormulaDimension.sameObject
                    ? `${record.inspectObjectId}` === `${_inspectObjectId}`
                    : `${record.sequence}` === `${_sequence}`,
                );
                if (_taskLineObject && _taskLineObject.cacheInspectObjectId) {
                  const _newTaskLineObjects = _taskLineObjects.filter(
                    record => record.cacheInspectObjectId !== _taskLineObject.cacheInspectObjectId,
                  );
                  // 后台删除act和dtl的Id
                  if (
                    _taskLineObject.inspectTaskObjectActId &&
                    !_inspectTaskObjectActIds.includes(_taskLineObject.inspectTaskObjectActId)
                  ) {
                    _inspectTaskObjectActIds.push(_taskLineObject.inspectTaskObjectActId);
                    (_taskLineObject.dtlIds || []).forEach(dtlId => {
                      if (dtlId && !_inspectDocLineActDtlIds.includes(dtlId)) {
                        _inspectDocLineActDtlIds.push(dtlId);
                      }
                    });
                    _taskLineObject.inspectTaskObjectActId = null;
                    _taskLineObject.dtlIds = [];
                  }
                  _newTaskLineObjects.splice(Number(_sequence || 1) - 1, 1);
                  _formulaItemRecord.set('taskLineObjects', _newTaskLineObjects);
                  _formulaItemRecord.status = 'update';
                }
              } else if (dimension === FormulaDimension.sameItem) {
                _formulaItemRecord.set('taskLineObjects', []);
                _formulaItemRecord.status = 'update';
                // 后台删除act和dtl的Id
                _taskLineObjects.forEach(objItem => {
                  if (
                    objItem.inspectTaskObjectActId &&
                    !_inspectTaskObjectActIds.includes(objItem.inspectTaskObjectActId)
                  ) {
                    _inspectTaskObjectActIds.push(objItem.inspectTaskObjectActId);
                    (objItem.dtlIds || []).forEach(dtlId => {
                      if (dtlId && !_inspectDocLineActDtlIds.includes(dtlId)) {
                        _inspectDocLineActDtlIds.push(dtlId);
                      }
                    });
                    objItem.inspectTaskObjectActId = null;
                    objItem.dtlIds = [];
                  }
                });
              }
            }
          } else if (modeType === ModeType.row) {
            if (dimension === FormulaDimension.sameSequence) {
              const _sequence = Number(valueName.split('_VALUE')[1] || 0);
              _formulaItemRecord.set(`${fieldName}_VALUE${_sequence}`, null);
              _formulaItemRecord.set(`${fieldName}_VALUE${_sequence}_COLOR`, null);
            } else if (dimension === FormulaDimension.sameItem) {
              const _formulaCount = Number(_formulaItemRecord.get('formulaCount') || 0);
              for (let i = 0; i < _formulaCount; i++) {
                _formulaItemRecord.set(`${fieldName}_VALUE${i}`, null);
                _formulaItemRecord.set(`${fieldName}_VALUE${i}_COLOR`, null);
              }
            }
          }
        }
      });

      if (_params.length > 0) {
        const res = await getCalculateFormula({
          params: _params,
        });
        if (res && res.success) {
          const data = res.rows || [];
          if (modeType === ModeType.col) {
            _params.forEach(item => {
              const { formulaItemId, dimension } = item;
              const _fieldName = _itemIdFieldNameObj[`ITEM_ID_${formulaItemId}`];
              const _resultList = data.filter(result => result.formulaItemId === formulaItemId);
              const _cacheInspectObjectId = curRecord.get('cacheInspectObjectId');

              const _formulaCount = Number(curRecord.get(`${_fieldName}_FORMULA_COUNT`) || 0);
              // 后台删除act和dtl的Id
              for (let i = 0; i < _formulaCount; i++) {
                const _itemColKey = `KEY${_cacheInspectObjectId}_${_fieldName}_VALUE${i}`;
                const _cacheDtlInfo = _itemColDtlObj[_itemColKey] || {};
                const _dtlId = _cacheDtlInfo?.inspectDocLineActDtlId;
                if (_dtlId && !_inspectDocLineActDtlIds.includes(_dtlId)) {
                  _inspectDocLineActDtlIds.push(_dtlId);
                  _itemColDtlObj[_itemColKey] = {};
                }
              }

              const _cacheItemInfo = _cacheItemData[_fieldName] || {};
              const _falseValues = _cacheItemInfo.falseValues || [];
              const _warningValues = _cacheItemInfo.warningValues || [];
              const _trueValues = _cacheItemInfo.trueValues || [];

              const _resultLen = _resultList.length;
              if ([FormulaDimension.sameObject, FormulaDimension.sameSequence].includes(dimension)) {
                const _maxCount = _formulaCount >= _resultLen ? _formulaCount : _resultLen;
                for (let i = 0; i < _maxCount; i++) {
                  const _newValueName = `${_fieldName}_VALUE${i}`;
                  if (i >= _resultLen) {
                    curRecord.set(_newValueName, null);
                    curRecord.set(`${_newValueName}_COLOR`, null);
                  } else {
                    const _newValue = _resultList[i].calculationResult;
                    curRecord.set(_newValueName, _newValue);
                    if (_newValue || _newValue === 0) {
                      const _color = onGetValueColor(
                        _newValue,
                        'CALCULATE_FORMULA',
                        _falseValues,
                        _warningValues,
                        _trueValues,
                      );
                      curRecord.set(`${valueName}_COLOR`, _color);
                    } else {
                      curRecord.set(`${valueName}_COLOR`, null);
                    }
                  }
                }
                curRecord.set(`${_fieldName}_FORMULA_COUNT`, _resultList.length);
              } else if (dimension === FormulaDimension.sameItem) {
                _cacheInspectObjectList.forEach((_inspectObjectRecord, index) => {
                  const _formulaCount = Number(
                    _inspectObjectRecord.get(`${_fieldName}_FORMULA_COUNT`) || 0,
                  );
                  if (index === 0 && _resultLen > 0) {
                    const _maxCount = _formulaCount >= _resultLen ? _formulaCount : _resultLen;
                    for (let i = 0; i < _maxCount; i++) {
                      const _newValueName = `${_fieldName}_VALUE${i}`;
                      if (i >= _resultLen) {
                        _inspectObjectRecord.set(_newValueName, null);
                        _inspectObjectRecord.set(`${_newValueName}_COLOR`, null);
                      } else {
                        const _newValue = _resultList[i].calculationResult;
                        _inspectObjectRecord.set(_newValueName, _newValue);
                        if (_newValue || _newValue === 0) {
                          const _color = onGetValueColor(
                            _newValue,
                            'CALCULATE_FORMULA',
                            _falseValues,
                            _warningValues,
                            _trueValues,
                          );
                          _inspectObjectRecord.set(`${valueName}_COLOR`, _color);
                        } else {
                          _inspectObjectRecord.set(`${valueName}_COLOR`, null);
                        }
                      }
                    }
                    _inspectObjectRecord.set(`${_fieldName}_FORMULA_COUNT`, _resultLen);
                  } else {
                    for (let i = 0; i < _formulaCount; i++) {
                      const _newValueName = `${_fieldName}_VALUE${i}`;
                      _inspectObjectRecord.set(_newValueName, null);
                      _inspectObjectRecord.set(`${_newValueName}_COLOR`, null);
                    }
                    _inspectObjectRecord.set(`${_fieldName}_FORMULA_COUNT`, 0);
                  }
                });
              }
            });
          } else if (modeType === ModeType.rowObj) {
            _params.forEach(item => {
              const { formulaItemId, dimension } = item;
              const formulaList = (item.formulaList || []).filter(d => d.inspectItemId);
              const _formulaItemRecord = _calculateFormulaRecordInfo[`ITEM_ID_${formulaItemId}`];
              if (_formulaItemRecord) {
                const _taskLineObjects = _formulaItemRecord.get('taskLineObjects') || [];
                const _fieldName = _itemIdFieldNameObj[`ITEM_ID_${formulaItemId}`];
                const _resultList = data.filter(result => result.formulaItemId === formulaItemId);
                const _dataType = _formulaItemRecord.get('dataType');
                const _falseValues = _formulaItemRecord.get('falseValues');
                const _warningValues = _formulaItemRecord.get('warningValues');
                const _trueValues = _formulaItemRecord.get('trueValues');

                const _resultLen = _resultList.length;
                if (
                  [FormulaDimension.sameObject, FormulaDimension.sameSequence].includes(dimension)
                ) {
                  const { inspectObjectId, objectInspectSequence } = formulaList[0];
                  const _taskLineObject = _taskLineObjects.find(record =>
                    dimension === FormulaDimension.sameObject
                      ? `${record.inspectObjectId}` === `${inspectObjectId}`
                      : `${record.sequence}` === `${objectInspectSequence}`,
                  );
                  let _newTaskLineObjects = _taskLineObjects;
                  if (_taskLineObject && _taskLineObject.cacheInspectObjectId) {
                    _newTaskLineObjects = _taskLineObjects.filter(
                      record => record.cacheInspectObjectId !== _taskLineObject.cacheInspectObjectId,
                    );
                    // 后台删除act和dtl的Id
                    if (
                      _taskLineObject.inspectTaskObjectActId &&
                      !_inspectTaskObjectActIds.includes(_taskLineObject.inspectTaskObjectActId)
                    ) {
                      _inspectTaskObjectActIds.push(_taskLineObject.inspectTaskObjectActId);
                      (_taskLineObject.dtlIds || []).forEach(dtlId => {
                        if (dtlId && !_inspectDocLineActDtlIds.includes(dtlId)) {
                          _inspectDocLineActDtlIds.push(dtlId);
                        }
                      });
                      _taskLineObject.inspectTaskObjectActId = null;
                      _taskLineObject.dtlIds = [];
                    }
                    // 目标对象存在：如果没有返回结果则清空该行，如果有返回值则重新赋值
                    if (_resultLen > 0) {
                      const _formulaCount = Number(
                        _taskLineObject[`${_fieldName}_FORMULA_COUNT`] || 0,
                      );
                      const _maxCount = _formulaCount >= _resultLen ? _formulaCount : _resultLen;
                      for (let i = 0; i < _maxCount; i++) {
                        if (i >= _resultLen) {
                          const _newValueName = `${_fieldName}_VALUE${i}`;
                          _taskLineObject[_newValueName] = null;
                          _taskLineObject[`${_fieldName}_VALUE${i}_ID`] = null;
                          _taskLineObject[`${_newValueName}_COLOR`] = null;
                        } else {
                          const _newValue = _resultList[i].calculationResult;
                          _taskLineObject[`${_fieldName}_VALUE${i}`] = _newValue;
                          if (_newValue || _newValue === 0) {
                            _taskLineObject[`${_fieldName}_VALUE${i}_COLOR`] = onGetValueColor(
                              _newValue,
                              _dataType,
                              _falseValues,
                              _warningValues,
                              _trueValues,
                            );
                          } else {
                            _taskLineObject[`${_fieldName}_VALUE${i}_COLOR`] = null;
                          }
                        }
                      }
                      _taskLineObject[`${_fieldName}_FORMULA_COUNT`] = _resultLen;
                      _newTaskLineObjects.splice(
                        Number(objectInspectSequence || 1) - 1,
                        0,
                        _taskLineObject,
                      );
                    }
                    _formulaItemRecord.set('taskLineObjects', _newTaskLineObjects);
                    _formulaItemRecord.status = 'update';
                  } else if (_resultLen > 0) {
                    // 目标对象不存在但有返回结果时则自动添加一行对象
                    const _addObjInfo = {
                      cacheInspectObjectId: uuid(),
                      fieldName: _fieldName,
                      dataType: _dataType,
                      inspectObjectId,
                      sourceObjectCode:
                        dimension === FormulaDimension.sameObject
                          ? curRecord.get('sourceObjectCode')
                          : null,
                      sequence: objectInspectSequence,
                    };
                    _addObjInfo[`${_fieldName}_FORMULA_COUNT`] = _resultLen;
                    _resultList.forEach((result, index) => {
                      const _newValue = result.calculationResult;
                      _addObjInfo[`${_fieldName}_VALUE${index}`] = _newValue;
                      if (_newValue || _newValue === 0) {
                        _addObjInfo[`${_fieldName}_VALUE${index}_COLOR`] = onGetValueColor(
                          _newValue,
                          _dataType,
                          _falseValues,
                          _warningValues,
                          _trueValues,
                        );
                      } else {
                        _addObjInfo[`${_fieldName}_VALUE${index}_COLOR`] = null;
                      }
                    });
                    _taskLineObjects.push(_addObjInfo);
                    _formulaItemRecord.set('taskLineObjects', _taskLineObjects);
                    _formulaItemRecord.status = 'update';
                  }
                } else if (dimension === FormulaDimension.sameItem) {
                  // 后台删除act和dtl的Id
                  _taskLineObjects.forEach(objItem => {
                    if (
                      objItem.inspectTaskObjectActId &&
                      !_inspectTaskObjectActIds.includes(objItem.inspectTaskObjectActId)
                    ) {
                      _inspectTaskObjectActIds.push(objItem.inspectTaskObjectActId);
                      (objItem.dtlIds || []).forEach(dtlId => {
                        if (dtlId && !_inspectDocLineActDtlIds.includes(dtlId)) {
                          _inspectDocLineActDtlIds.push(dtlId);
                        }
                      });
                      objItem.inspectTaskObjectActId = null;
                      objItem.dtlIds = [];
                    }
                  });
                  const _newTaskLineObjects: Array<any> = [];
                  if (_resultLen > 0) {
                    const _addObjInfo = {
                      cacheInspectObjectId: uuid(),
                      fieldName: _fieldName,
                      dataType: _dataType,
                      inspectObjectId: null,
                      sequence: 1,
                    };
                    _addObjInfo[`${_fieldName}_FORMULA_COUNT`] = _resultLen;
                    _resultList.forEach((result, index) => {
                      const _newValue = result.calculationResult;
                      _addObjInfo[`${_fieldName}_VALUE${index}`] = _newValue;
                      if (_newValue || _newValue === 0) {
                        _addObjInfo[`${_fieldName}_VALUE${index}_COLOR`] = onGetValueColor(
                          _newValue,
                          _dataType,
                          _falseValues,
                          _warningValues,
                          _trueValues,
                        );
                      } else {
                        _addObjInfo[`${_fieldName}_VALUE${index}_COLOR`] = null;
                      }
                      _newTaskLineObjects.push(_addObjInfo);
                    });
                  }
                  _formulaItemRecord.set('taskLineObjects', _newTaskLineObjects);
                  _formulaItemRecord.status = 'update';
                }
              }
            });
          } else if (modeType === ModeType.row) {
            _params.forEach(item => {
              const { formulaItemId, dimension } = item;
              const formulaList = (item.formulaList || []).filter(d => d.inspectDocLineId);
              const _formulaItemRecord = _calculateFormulaRecordInfo[`ITEM_ID_${formulaItemId}`];
              if (_formulaItemRecord) {
                const _fieldName = _itemIdFieldNameObj[`ITEM_ID_${formulaItemId}`];
                const _resultList = data.filter(result => result.formulaItemId === formulaItemId);
                const _formulaCount = _formulaItemRecord.get('formulaCount');
                const _falseValues = _formulaItemRecord.get('falseValues');
                const _warningValues = _formulaItemRecord.get('warningValues');
                const _trueValues = _formulaItemRecord.get('trueValues');

                const _resultLen = _resultList.length;
                if (dimension === FormulaDimension.sameSequence) {
                  const { objectInspectSequence } = formulaList[0];
                  const _sequence = Number(objectInspectSequence || 1);
                  const _newValueName = `${_fieldName}_VALUE${_sequence - 1}`;
                  if (_resultLen > 0) {
                    const _newValue = _resultList[0].calculationResult;
                    _formulaItemRecord.set(_newValueName, _newValue);
                    const _color = onGetValueColor(
                      _newValue,
                      'CALCULATE_FORMULA',
                      _falseValues,
                      _warningValues,
                      _trueValues,
                    );
                    _formulaItemRecord.set(`${_newValueName}_COLOR`, _color);
                    if (_sequence === 1) {
                      _formulaItemRecord.set(`inspectResult`, _color === 'red' ? 'NG' : 'OK');
                    }

                    if (_formulaCount < _sequence) {
                      _formulaItemRecord.set('formulaCount', _sequence);
                    }
                  } else {
                    _formulaItemRecord.set(_newValueName, null);
                    _formulaItemRecord.set(`${_newValueName}_COLOR`, null);
                  }
                } else if (dimension === FormulaDimension.sameItem) {
                  const _maxCount = _formulaCount >= _resultLen ? _formulaCount : _resultLen;
                  for (let i = 0; i < _maxCount; i++) {
                    const _newValueName = `${_fieldName}_VALUE${i}`;
                    if (i >= _resultLen) {
                      _formulaItemRecord.set(_newValueName, null);
                      _formulaItemRecord.set(`${_newValueName}_COLOR`, null);
                    } else {
                      const _newValue = _resultList[i].calculationResult;
                      _formulaItemRecord.set(_newValueName, _newValue);
                      const _color = onGetValueColor(
                        _newValue,
                        'CALCULATE_FORMULA',
                        _falseValues,
                        _warningValues,
                        _trueValues,
                      );
                      _formulaItemRecord.set(`${_newValueName}_COLOR`, _color);
                    }
                  }
                  _formulaItemRecord.set('formulaCount', _maxCount);
                }
              }
              // 获取公式类项目数值
              getFormulaCalculation(formulaItemId);

            });
          }
        }
      }

      pendingTableDS.setState('itemColUpdateCacheObj', {
        deleteActIds: _inspectTaskObjectActIds,
        deleteDtlIds: Array.from(new Set(_inspectDocLineActDtlIds)),
      });
      inspectItemColDS.setState('itemColDtlObj', _itemColDtlObj);
    };

    // 获取公式类项目检测值
    const getFormulaCalculation = (id) => {
      const params: { inspectDocLineId?: string, inspectItemId?: string } = {}
      const _formulaRecords = pendingTableDS.records.filter(
        record =>
          record.get('inspectDocLineId') === id,
      );
      if (_formulaRecords.length === 1) {
        const _requiredItemValidFlag = _formulaRecords.some(record => {
          const formulaList = (record.get('formulaObj') || {})?.formulaList || [];
          const _formulaCount = formulaList.length;
          params.inspectDocLineId = record.get('inspectDocLineId');
          params.inspectItemId = record.get('inspectItemId');
          if (_formulaCount > 1) {
            const _fieldName = record?.get('fieldName');
            for (let i = 0; i < 2; i++) {
              const _value: string | number = record.get(`${_fieldName}_VALUE${i}`);
              if (!_value || _value === 0) {
                return false;
              }
              params[`inspectValue${i + 1}`] = _value;
            }
            return true;
          }
          return false;
        });
        if (_requiredItemValidFlag) {
          formulaCalculation({
            params,
            onSuccess: (res) => {
              const _record = _formulaRecords[0];
              const _fieldName = _record?.get('fieldName');
              const _falseValues = _record.get('falseValues');
              const _warningValues = _record.get('warningValues');
              const _trueValues = _record.get('trueValues');
              (res || []).forEach((item, index) => {
                const _newValue = item.calculationResult;
                _record.set(`${_fieldName}_VALUE${index + 2}`, _newValue);
                const _color = onGetValueColor(
                  _newValue,
                  'CALCULATE_FORMULA',
                  _falseValues,
                  _warningValues,
                  _trueValues,
                );
                _record.set(`${_fieldName}_COLOR${index + 2}`, _color);
                if (index === 1) {
                  _record.set(`inspectResult`, _color === 'red' ? 'NG' : 'OK');
                }

              })
            },
          });
        }
      }
    };


    // 切换tab
    const handleChangeActiveKey = async activeKey => {
      searchTableDS.setQueryParameter('tableKey', activeKey);
      setTabKey(activeKey);
      setPendingSelectList([]);
      setUnclaimedSelectList([]);
      await handleSearchDifferenceTab(activeKey);
    };
    // 不同tab查询
    const handleSearchDifferenceTab = async activeKey => {
      if (activeKey === 'processTask') {
        pendingTableDS.queryDataSet = searchTableDS.queryDataSet;
        pendingTableDS.setQueryParameter('tableKey', TabKey[activeKey]);
        // pendingTableDS.setQueryParameter(
        //   'customizeUnitCode',
        //   `${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_MAIN.QUERY,${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_MAIN.TABLE`,
        // );
        await pendingTableDS.query().then(() => {
          setPendingCount(pendingTableDS.totalCount);
          onHandleRowData(pendingTableDS.toData());
          const arr = pendingSelectList.map((record: any) => record?.get('inspectDocLineId'));
          if (pendingSelectList.length > 0) {
            pendingTableDS.records.forEach((item) => {
              if (arr.includes(item.get('inspectDocLineId'))) {
                pendingTableDS.select(item)
              }
            })
          }
        });
      } else if (activeKey === 'receiveTask') {
        unclaimedTableDs.queryDataSet = searchTableDS.queryDataSet;
        unclaimedTableDs.setQueryParameter('tableKey', TabKey[activeKey]);
        // unclaimedTableDs.setQueryParameter(
        //   'customizeUnitCode',
        //   `${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_MAIN.QUERY,${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_MAIN.TABLE`,
        // );
        await unclaimedTableDs.query().then(() => {
          setUnclaimedCount(unclaimedTableDs.totalCount);
        });
      }
    };

    // 分配检验任务
    const handleDistributeTask = () => {
      distributeDS.current?.set(
        'inspectDocLineId',
        unclaimedSelectList.map((record: Record) => record.get('inspectDocLineId')),
      );
      Modal.open({
        ...drawerPropsC7n({
          ds: distributeDS,
        }),
        key: Modal.key(),
        title: intl.get(`${modelPrompt}.title.distributeTask`).d('分配检验任务'),
        drawer: false,
        destroyOnClose: true,
        closable: true,
        maskClosable: true,
        style: {
          width: 500,
        },
        contentStyle: {
          width: 500,
        },
        className: 'hmes-style-modal',
        children: (
          <Form dataSet={distributeDS} columns={1} labelWidth={80} style={{ marginRight: 20 }}>
            <Lov name="inspectorObj" />
          </Form>
        ),
        onOk: () => handleOkDistributeTask(),
      });
    };

    // 确定分配检验任务
    const handleOkDistributeTask = async () => {
      const validFlag = await distributeDS.validate();
      if (!validFlag) {
        return Promise.resolve(false);
      }
      return receiveTask({
        params: distributeDS?.current?.get('inspectDocLineId'),
        queryParams: {
          inspectorId: distributeDS?.current?.get('inspectorId'),
        },
        onSuccess: async () => {
          notification.success({});
          await handleSearchDifferenceTab(tabKey);
          setUnclaimedSelectList([]);
        },
      });
    };


    // 领取检验任务
    const handleReceiveTask = async () => {
      const inspectDocLineIds = unclaimedSelectList.map((record: Record) => record.get('inspectDocLineId'));
      return receiveTask({
        params: inspectDocLineIds,
        onSuccess: async () => {
          notification.success({});
          await handleSearchDifferenceTab(tabKey);
          setUnclaimedSelectList([]);
        },
      });
    };

    // 判断选中数据是否包含出参入参数组
    const handleIsTargetInspectItemIds = (checkedList) => {
      // 入参出参id汇总
      const _targetInspectItemIds = pendingTableDS.getState('targetInspectItemIds') || [];
      // 如果选中的数据中包含入参或出参，则提示用户选择全部入参或出参
      const isTargetInspectItemIds = checkedList.every((item) => {
        // 找到目标出参入参数组对象
        const findTargetItemArr = _targetInspectItemIds.find((targetArr) =>
          (targetArr || []).includes(item)
        );
        if (!findTargetItemArr) {
          return true;
        }
        return findTargetItemArr.every((targetItem) =>
          checkedList.includes(targetItem)
        );
      });
      // 如果选中的数据中包含入参或出参，则提示用户选择全部入参或出参
      if (!isTargetInspectItemIds) {
        notification.warning({
          message: intl
            .get(`${modelPrompt}.message.revokeStatusValidTip`)
            .d(`选中数据中包含入参和出参项目，请选择全部入参或出参！`),
        });
        return false;
      }
      return true;
    };

    // 撤销检验任务
    const handleRevokeTask = async () => {
      const inspectTaskList = pendingSelectList;
      const inspectTaskIds = inspectTaskList.map((record: Record) => record.get('inspectDocLineId'));
      // 判断选中数据是否包含出参入参数组
      if (!handleIsTargetInspectItemIds(inspectTaskIds)) {
        return;
      }
      const revokeInspectTaskList = inspectTaskList.filter(
        (record: Record) =>
          record.get('inspectDocLineStatus') === 'RELEASED');
      if (revokeInspectTaskList.length !== inspectTaskList.length) {
        Modal.confirm({
          title: intl
            .get(`${modelPrompt}.message.revokeStatusValidTip`)
            .d(`您勾选的数据中包含了非下达的数据，这些数据将不做处理，是否继续？`),
          onOk: () => handleOkRevokeTask(inspectTaskIds),
        });
      } else {
        await handleOkRevokeTask(inspectTaskIds);
      }
    };

    // 确定撤销检验任务
    const handleOkRevokeTask = async inspectTaskIds => {
      if (inspectTaskIds.length < 1) {
        notification.success({});
        await handleSearchDifferenceTab(tabKey);
        setPendingSelectList([]);
      } else {
        // const inspectTaskIds = inspectTaskList.map((record: Record) => record.get('inspectDocLineId'));
        revokeTask({
          params: inspectTaskIds,
          onSuccess: async () => {
            notification.success({});
            await handleSearchDifferenceTab(tabKey);
            setPendingSelectList([]);
          },
        });
      }
    };

    // 设置录入值宽度
    // 抽样数量改变时自动计算录入值列宽
    const handleChangeInspectTextWidth = () => {
      const inspectBusinessType = pendingTableDS?.current.get('inspectBusinessType');
      let maxQtyRecord;
      if (inspectBusinessType === 'RATO-IQC' || inspectBusinessType === 'RATO-SQC') {
        maxQtyRecord = maxBy(pendingTableDS.records, record => {
          const _frequencyQtyCount = !isNaN(record?.get('recordFrequency'))
            ? Number(record?.get('recordFrequency') || 0)
            : 1;
          let _count = Number(record.get('samplingQtyCount') || 0);
          if (_frequencyQtyCount > 1) {
            _count = Number(_frequencyQtyCount) <= _count ? Number(_frequencyQtyCount) : _count;
          } else {
            _count = _count >= 1 ? 1 : _count;
          }
          return _count + Number(record.get('addQtyCount') || 0);
        });
      } else {
        maxQtyRecord = maxBy(
          pendingTableDS.records,
          record =>
            Number(record.get('samplingQtyCount') || 0) + Number(record.get('addQtyCount') || 0),
        );
      }

      const maxQty =
        Number(maxQtyRecord?.get('samplingQtyCount') || 0) +
        Number(maxQtyRecord.get('addQtyCount') || 0);
      if (maxQty > 2) {
        setCacheMinWidth(maxQty * 65 + (maxQty - 1) * 5 + 16);
      } else {
        setCacheMinWidth(180);
      }
      pendingTableDS.setState('maxSamplingQty', maxQty);
    };

    const handleChangeinspectResult = (newValue, oldValue, record) => {
      if (newValue !== oldValue) {
        // const _qty = Number(record.get('qty') || 0);
        // if (newValue === 'OK') {
        //   record.set('ngQty', 0);
        //   record.set('scrapQty', 0);
        //   record.set('okQty', _qty);
        // } else if (newValue === 'NG') {
        //   record.set('ngQty', _qty);
        //   record.set('scrapQty', 0);
        //   record.set('okQty', 0);
        // }
      }
    };

    // 行列模式-录入值变化颜色处理
    const handleChangeValueColor = (fieldName, value, valueName, record, dataType) => {
      let falseValues: Array<any>;
      let warningValues: Array<any>;
      let trueValues: Array<any>;

      const _modeType = inspectItemRowDS.getState('modeType');
      if (_modeType === ModeType.col) {
        const _cacheItemData = inspectItemColDS.getState('cacheItemData') || {};
        const _cacheInfo = _cacheItemData ? _cacheItemData[fieldName] : {};
        falseValues = _cacheInfo?.falseValues;
        warningValues = _cacheInfo?.warningValues;
        trueValues = _cacheInfo?.trueValues;
      } else {
        falseValues = record?.get('falseValues');
        warningValues = record?.get('warningValues');
        trueValues = record?.get('trueValues');
      }

      if (value || value === 0) {
        const _color = onGetValueColor(value, dataType, falseValues, warningValues, trueValues);
        record.set(`${valueName}_COLOR`, _color);
      } else {
        record.set(`${valueName}_COLOR`, null);
      }

      handleComputedQty(fieldName, dataType, false, true, false, true, valueName);
    };

    // 抽样数量改变时自动计算录入值列宽
    const handleChangeSamplingQty = (newValue, oldValue, fieldName, dataType, curRecord) => {
      if (newValue !== oldValue) {
        let _dataQty = 0;
        if (
          curRecord.get('dataQtyDisposition') === 'DATA' ||
          curRecord.get('dataQtyDisposition') === 'SAMPLE'
        ) {
          _dataQty = 1;
        } else {
          _dataQty = Number(curRecord.get('dataQty') || 0);
        }
        curRecord.set('samplingQtyCount', _dataQty * Number(newValue || 0));
        handleComputedQty(fieldName, dataType, true, true, false, true, '');
        const _maxSamplingQty = Number(
          maxBy(pendingTableDS.records, record => record.get('samplingQtyCount'))?.get(
            'samplingQtyCount',
          ) || 0,
        );
        if (_maxSamplingQty > 2) {
          setCacheMinWidth(_maxSamplingQty * 65 + (_maxSamplingQty - 1) * 5 + 16);
        } else {
          setCacheMinWidth(180);
        }
        inspectItemRowDS.setState('maxSamplingQty', _maxSamplingQty);
      }
    };


    // 待处理检验项目
    const columns: ColumnProps[] = useMemo(() => {
      return [
        {
          name: 'inspectDocNum',
          minWidth: 120,
          lock: ColumnLock.left,
          renderer: ({ value, record }) => {
            return (
              <a onClick={() => {
                openTab({
                  key: '/hwms/inspect-doc-maintain/dist',
                  path: `/hwms/inspect-doc-maintain/dist/${record?.get('inspectDocId')}`,
                });
              }}>
                {value}
              </a>
            );
          },
        },
        {
          name: 'inspectTaskCode',
          width: 160,
          lock: ColumnLock.left,
          renderer: ({ value, record }) => {
            // 跳转详情
            return (
              <a
                onClick={() => {
                  openTab({
                    key: '/hwms/inspection-platform/dist',
                    path: `/hwms/inspection-platform/dist/${record?.get('inspectTaskId')}`,
                  });
                }}
              >
                {value}
              </a>
            );
          },
        },
        {
          name: 'inspectBusinessType',
        },
        {
          name: 'inspectItemDesc',
        },
        {
          name: 'materialCode',
        },
        {
          name: 'materialName',
        },
        {
          name: 'valueRange',
          width: 180,
          title: intl.get(`${modelPrompt}.model.line.valueRange`).d('符合值/不符合值/预警值'),
          tooltip: Tooltip.none,
          renderer: ({ record }) => {
            return (
              <Popover
                placement="top"
                content={
                  <div>
                    {(record?.get('trueValues') || []).map(item => (
                      <div
                        className={styles['table-tooltip-tag']}
                        style={{ color: '#11d954', backgroundColor: '#E6FFEA' }}
                      >
                        {item}
                      </div>
                    ))}
                    {(record?.get('falseValues') || []).map(item => (
                      <div
                        className={styles['table-tooltip-tag']}
                        style={{ color: '#f23a50', backgroundColor: '#fff0f0' }}
                      >
                        {item}
                      </div>
                    ))}
                    {(record?.get('warningValues') || []).map(item => (
                      <div
                        className={styles['table-tooltip-tag']}
                        style={{ color: '#fbad00', backgroundColor: '#fffbe6' }}
                      >
                        {item}
                      </div>
                    ))}
                    &nbsp;{record?.get('uomName')}
                  </div>
                }
                trigger="hover"
              >
                <div>
                  {(record?.get('trueValues') || []).map(item => (
                    <Tag color="green">{item}</Tag>
                  ))}
                  {(record?.get('falseValues') || []).map(item => (
                    <Tag color="red">{item}</Tag>
                  ))}
                  {(record?.get('warningValues') || []).map(item => (
                    <Tag color="yellow">{item}</Tag>
                  ))}
                  &nbsp;{record?.get('uomName')}
                </div>
              </Popover>
            );
          },
        },
        {
          name: 'sampleQty',
          // header: (...args: Array<any>) => {
          //   return (
          //     <span
          //       // onClick={() => handleBatchChangeData('BATCH_SAMPLING')}
          //       style={{ cursor: 'pointer' }}
          //     >
          //       {args[2]}
          //     </span>
          //   );
          // },
          minWidth: 150,
          align: ColumnAlign.left,
          renderer: ({ value, record }) => {
            if (!record) {
              return;
            }
            let _canEdit = false;
            const _samplingType = record?.get('samplingType');
            const dataQty = record?.get('dataQty');
            const _dataQtyDisposition = record?.get('dataQtyDisposition');
            // 抽样方式不等于自定义抽样，或者dataQtyDisposition等于DATA时，不显示抽样数量列
            if (_samplingType !== 'USER_DEFINED_SAMPLING' || _dataQtyDisposition === 'DATA') {
              if (!value) {
                record.set('sampleQty', dataQty);
              }
              return (
                <span>
                  {value}
                  &nbsp;{record?.get('uomName')}
                </span>
              );
            }
            const _fieldName = record.get('fieldName');
            const _dataType = record.get('dataType');
            return (
              <span>
                <NumberField
                  record={record}
                  name="sampleQty"
                  // disabled={!_canEdit}
                  style={{ width: 80, verticalAlign: 'baseline' }}
                  onChange={(newValue, oldValue) =>
                    handleChangeSamplingQty(newValue, oldValue, _fieldName, _dataType, record)
                  }
                />
                &nbsp;{record?.get('uomName')}
              </span>
            );
          },
        },
        {
          name: 'inspectResult',
          // header: (...args: Array<any>) => {
          //   return (
          //     <a onClick={() => handleBatchChangeData('BATCH_RESULT')} style={{ cursor: 'pointer' }}>
          //       {args[2]}
          //     </a>
          //   );
          // },
          width: 120,
          renderer: ({ value, record }) => {
            if (!record) {
              return '';
            }
            let _canEdit = false;
            // if (lovList.includes(pendingTableDS?.current?.get('inspectBusinessType'))) {
            //   if (pendingTableDS.getState('canEdit') && record.get('inspectorId') === userInfo?.id) {
            //     _canEdit = pendingTableDS.getState('canEdit');
            //   }
            // } else {
            //   _canEdit = pendingTableDS.getState('canEdit');
            // }
            return (
              <Select
                record={record}
                name="inspectResult"
                // readOnly={!pendingTableDS.getState('canEdit')}
                // readOnly={!_canEdit}
                onChange={(newValue, oldValue) => handleChangeinspectResult(newValue, oldValue, record)}
                style={{
                  verticalAlign: 'baseline',
                  backgroundColor:
                    value === 'OK' ? 'rgb(230, 255, 234)' : value === 'NG' ? 'rgb(255, 240, 240)' : '',
                }}
              />
            );
          },
        },
        {
          name: 'inspectValueRecord',
          width: cacheMinWidth,
          minWidth: cacheMinWidth,
          defaultWidth: cacheMinWidth,
          renderer: ({ record }) => {
            if (!record) {
              return;
            }
            // const _canEdit = pendingTableDS.getState('canEdit');
            // let _canEdit = false;
            const _dataType = record?.get('dataType');
            const _samplingQtyCount = Number(record?.get('samplingQtyCount') || 0);
            const _addQtyCount = Number(record?.get('addQtyCount') || 0);
            const _fieldName = record?.get('fieldName');
            let _count =
              _dataType === 'CALCULATE_FORMULA'
                ? Number(record?.get('formulaCount') || 0)
                : _samplingQtyCount;
            // 取最大的数
            let taskLineObjects = 0;
            record?.get('taskLineObjects')?.forEach(taskLineObjectItem => {
              if (taskLineObjectItem.taskLineActDtls && taskLineObjectItem.taskLineActDtls.length > 0) {
                taskLineObjects += taskLineObjectItem.taskLineActDtls.length;
              } else {
                taskLineObjects++;
              }
            });
            if (taskLineObjects > _count) {
              _count = taskLineObjects;
            }

            _count += _addQtyCount;

            const _dataQtyValue: Array<any> = [];
            for (let i = 0; i < _count; i++) {
              _dataQtyValue.push(`${_fieldName}_VALUE${i}`);
            }
            const _trueValues = record?.get('trueValues') || [];
            const _falseValues = record?.get('falseValues') || [];
            const _valueLists = record?.get('valueLists') || [];
            const _decimalNumber = record?.get('decimalNumber');
            // 如果是公式类项目
            if (_dataType === 'CALCULATE_FORMULA' && _dataQtyValue.length === 2) {
              const inspectValue1 = record?.get(`${_fieldName}_VALUE2`);
              const inspectValue2 = record?.get(`${_fieldName}_VALUE3`);
              if (inspectValue1 && inspectValue2) {
                _dataQtyValue.push(`${_fieldName}_VALUE2`);
                _dataQtyValue.push(`${_fieldName}_VALUE3`);
                _count += 2;
              }
            }

            // 计算列宽
            const _valueWidth = `${100 / _count}%`;
            record.set('_dataQtyValue', _dataQtyValue);
            return (
              <ItemGroup>
                {_dataQtyValue.map((valueName, index) => {
                  const _formulaValue = record?.get(valueName);
                  return (
                    <>
                      {_dataType === 'VALUE' && !_decimalNumber && _decimalNumber !== 0 && (
                        <Item name={valueName}>
                          <NumberField
                            onEnterDown={() => {
                              if (pendingTableDS.current?.get('enterFlag') !== 'Y') {
                                return;
                              }
                              // 新增字段
                              // 获取之前的输入框是否为必输
                              const required = record?.getField(valueName)?.required;
                              const type = record?.getField(valueName)?.type;
                              record.addField(`${_fieldName}_VALUE${_dataQtyValue.length}`, {
                                name: `${_fieldName}_VALUE${_dataQtyValue.length}`,
                                type,
                                required,
                              });
                              record.set('addQtyCount', _addQtyCount + 1);
                              handleChangeInspectTextWidth();

                              // 聚焦
                              setTimeout(() => {
                                const elementInput = document.getElementsByName(
                                  `${_fieldName}_VALUE${_dataQtyValue.length}`,
                                );
                                if (elementInput.length > 0) {
                                  elementInput[0].focus();
                                }
                              }, 100);
                            }}
                            record={record}
                            name={valueName}
                            style={{
                              width: _valueWidth,
                              verticalAlign: 'baseline',
                              marginRight: index !== _dataQtyValue.length - 1 ? 3 : 0,
                            }}
                            className={
                              record?.get(`${valueName}_COLOR`)
                                ? `${styles[`input-color-${record?.get(`${valueName}_COLOR`)}`]} ${styles['number-input-left']
                                }`
                                : styles['number-input-left']
                            }
                            // disabled={!_canEdit}
                            onChange={(value, oldValue) => {
                              if (
                                !(
                                  (value === undefined || value === null) &&
                                  (oldValue === undefined || oldValue === null)
                                )
                              ) {
                                handleChangeValueColor(_fieldName, value, valueName, record, _dataType);
                              }
                            }}
                          />
                        </Item>
                      )}
                      {_dataType === 'VALUE' && isNumber(_decimalNumber) && _decimalNumber >= 0 && (
                        <Item name={valueName}>
                          <Currency
                            onEnterDown={() => {
                              if (pendingTableDS.current?.get('enterFlag') !== 'Y') {
                                return;
                              }
                              // 新增字段
                              // 获取之前的输入框是否为必输
                              const required = record?.getField(valueName)?.required;
                              const type = record?.getField(valueName)?.type;
                              record.addField(`${_fieldName}_VALUE${_dataQtyValue.length}`, {
                                name: `${_fieldName}_VALUE${_dataQtyValue.length}`,
                                type,
                                required,
                              });
                              record.set('addQtyCount', _addQtyCount + 1);
                              handleChangeInspectTextWidth();

                              // 聚焦
                              setTimeout(() => {
                                const elementInput = document.getElementsByName(
                                  `${_fieldName}_VALUE${_dataQtyValue.length}`,
                                );
                                if (elementInput.length > 0) {
                                  elementInput[0].focus();
                                }
                              }, 100);
                            }}
                            record={record}
                            name={valueName}
                            precision={_decimalNumber > 6 ? 6 : _decimalNumber}
                            style={{
                              width: _valueWidth,
                              verticalAlign: 'baseline',
                              marginRight: index !== _dataQtyValue.length - 1 ? 3 : 0,
                            }}
                            className={
                              record?.get(`${valueName}_COLOR`)
                                ? `${styles[`input-color-${record?.get(`${valueName}_COLOR`)}`]} ${styles['number-input-left']
                                }`
                                : styles['number-input-left']
                            }
                            // disabled={!_canEdit}
                            onChange={(value, oldValue) => {
                              if (
                                !(
                                  (value === undefined || value === null) &&
                                  (oldValue === undefined || oldValue === null)
                                )
                              ) {
                                handleChangeValueColor(_fieldName, value, valueName, record, _dataType);
                              }
                            }}
                          />
                        </Item>
                      )}
                      {_dataType === 'DECISION_VALUE' && (
                        <Item name={valueName}>
                          <Select
                            record={record}
                            name={valueName}
                            style={{
                              width: _valueWidth,
                              verticalAlign: 'baseline',
                              marginRight: index !== _dataQtyValue.length - 1 ? 3 : 0,
                            }}
                            className={
                              record?.get(`${valueName}_COLOR`)
                                ? styles[`select-color-${record?.get(`${valueName}_COLOR`)}`]
                                : ''
                            }
                            // disabled={!_canEdit}
                            onChange={value =>
                              handleChangeValueColor(_fieldName, value, valueName, record, _dataType)
                            }
                          >
                            {_trueValues.concat(_falseValues).map(item => (
                              <Option value={item} key={item}>
                                {item}
                              </Option>
                            ))}
                          </Select>
                        </Item>
                      )}
                      {_dataType === 'TEXT' && (
                        <Item name={valueName}>
                          <TextField
                            onEnterDown={() => {
                              if (pendingTableDS.current?.get('enterFlag') !== 'Y') {
                                return;
                              }
                              // 新增字段
                              // 获取之前的输入框是否为必输
                              const required = record?.getField(valueName)?.required;
                              const type = record?.getField(valueName)?.type;
                              record.addField(`${_fieldName}_VALUE${_dataQtyValue.length}`, {
                                name: `${_fieldName}_VALUE${_dataQtyValue.length}`,
                                type,
                                required,
                              });
                              record.set('addQtyCount', _addQtyCount + 1);
                              handleChangeInspectTextWidth();

                              // 聚焦
                              setTimeout(() => {
                                const elementInput = document.getElementsByName(
                                  `${_fieldName}_VALUE${_dataQtyValue.length}`,
                                );
                                if (elementInput.length > 0) {
                                  elementInput[0].focus();
                                }
                              }, 100);
                            }}
                            record={record}
                            name={valueName}
                            style={{
                              width: _valueWidth,
                              verticalAlign: 'baseline',
                              marginRight: index !== _dataQtyValue.length - 1 ? 3 : 0,
                            }}
                            // disabled={!_canEdit}
                            onChange={() =>
                              handleComputedQty(
                                _fieldName,
                                _dataType,
                                false,
                                true,
                                record,
                                true,
                                valueName,
                              )
                            }
                          />
                        </Item>
                      )}
                      {_dataType === 'VALUE_LIST' && (
                        <Item name={valueName}>
                          <Select
                            record={record}
                            name={valueName}
                            style={{
                              width: _valueWidth,
                              verticalAlign: 'baseline',
                              marginRight: index !== _dataQtyValue.length - 1 ? 3 : 0,
                            }}
                            className={
                              record?.get(`${valueName}_COLOR`)
                                ? styles[`select-color-${record?.get(`${valueName}_COLOR`)}`]
                                : ''
                            }
                            // disabled={!_canEdit}
                            onChange={value =>
                              handleChangeValueColor(_fieldName, value, valueName, record, _dataType)
                            }
                          >
                            {_valueLists.map(item => (
                              <Option value={item} key={item}>
                                {item}
                              </Option>
                            ))}
                          </Select>
                        </Item>
                      )}
                      {_dataType === 'DATE' && (
                        <Item name={valueName}>
                          <DateTimePicker
                            record={record}
                            name={valueName}
                            style={{
                              width: _valueWidth,
                              verticalAlign: 'baseline',
                              marginRight: index !== _dataQtyValue.length - 1 ? 3 : 0,
                            }}
                            // disabled={!_canEdit}
                            onChange={() =>
                              handleComputedQty(
                                _fieldName,
                                _dataType,
                                false,
                                true,
                                record,
                                true,
                                valueName,
                              )
                            }
                          />
                        </Item>
                      )}
                      {_dataType === 'CALCULATE_FORMULA' && (_formulaValue || _formulaValue === 0) && (
                        <Item name={valueName}>
                          <NewTooltip placement="top" title={formulaPromptArray[index]}>
                            <TextField
                              onEnterDown={() => {
                                if (pendingTableDS.current?.get('enterFlag') !== 'Y') {
                                  return;
                                }
                                // 新增字段
                                // 获取之前的输入框是否为必输
                                const required = record?.getField(valueName)?.required;
                                const type = record?.getField(valueName)?.type;
                                record.addField(`${_fieldName}_VALUE${_dataQtyValue.length}`, {
                                  name: `${_fieldName}_VALUE${_dataQtyValue.length}`,
                                  type,
                                  required,
                                });
                                record.set('addQtyCount', _addQtyCount + 1);
                                handleChangeInspectTextWidth();

                                // 聚焦
                                setTimeout(() => {
                                  const elementInput = document.getElementsByName(
                                    `${_fieldName}_VALUE${_dataQtyValue.length}`,
                                  );
                                  if (elementInput.length > 0) {
                                    elementInput[0].focus();
                                  }
                                }, 100);
                              }}
                              record={record}
                              name={valueName}
                              style={{
                                // width: _valueWidth,
                                verticalAlign: 'baseline',
                                marginRight: index !== _dataQtyValue.length - 1 ? 3 : 0,
                              }}
                              className={
                                record?.get(`${valueName}_COLOR`)
                                  ? styles[`select-color-${record?.get(`${valueName}_COLOR`)}`]
                                  : ''
                              }
                              disabled
                            />
                          </NewTooltip>
                        </Item>
                      )}
                    </>
                  );
                })}
              </ItemGroup>
            );
          },
        },
        {
          name: 'remark',
          editor: true,
          width: 120,
        },
        {
          header: intl.get('tarzan.common.label.action').d('操作'),
          width: 580,
          renderer: ({ record }) => {
            return (
              <>
                <Attachment
                  {...attachmentProps1}
                  record={record}
                  name="otherEnclosure"
                  readOnly
                // disabled={!pendingTableDS.getState('canEdit')}
                />
                <Attachment
                  {...attachmentProps}
                  record={record}
                  name="enclosure"
                  readOnly
                // disabled={!pendingTableDS.getState('canEdit')}
                />
                <Attachment
                  {...attachmentProps}
                  record={record}
                  name="actEnclosure"
                // disabled={!pendingTableDS.getState('canEdit')}
                />
                <NcRecordComponent
                  // canEdit={pendingTableDS.getState('canEdit')}
                  canEdit={true}

                  type="text"
                  // visible={true}
                  visible={
                    pendingTableDS.current?.get('inspectNcRecordDimension') ===
                    NcRecordDimension.itemNc && !!record?.get('inspectTaskLineId')
                  }
                  customizeTable={customizeTable}
                  queryParams={{
                    inspectTaskId: pendingTableDS.current?.get('inspectTaskId'),
                    inspectDocId: pendingTableDS.current?.get('inspectDocId'),
                    inspectTaskLineId: record?.get('inspectTaskLineId'),
                    inspectItemId: record?.get('inspectItemId'),
                    inspectDocLineId: record?.get('inspectDocLineId'),
                    inspectItemDesc: record?.get('inspectItemDesc'),
                    inspectNcRecordDimension: pendingTableDS.current?.get('inspectNcRecordDimension'),
                  }}
                  style={{ marginLeft: '0.16rem' }}
                />
              </>
            );
          },
        },
      ];
    }, [cacheMinWidth]);

    // 待领取检验项目
    const unclaimedColumns: ColumnProps[] = [
      {
        name: 'inspectDocNum',
        lock: ColumnLock.left,
        renderer: ({ value, record }) => {
          return (
            <a onClick={() => {
              history.push(`/hwms/inspect-doc-maintain/dist/${record?.get('inspectDocId')}`);
            }}>
              {value}
            </a>
          );
        },
      },
      {
        name: 'inspectTaskCode',
        width: 160,
        lock: ColumnLock.left,
        renderer: ({ value, record }) => {
          // 跳转详情
          return (
            <a
              onClick={() => {
                history.push(`/hwms/inspection-platform/dist/${record?.get('inspectTaskId')}`);
              }}
            >
              {value}
            </a>
          );
        },
      },
      {
        name: 'inspectBusinessTypeDesc',
      },
      {
        name: 'inspectItemDesc',
      },
      {
        name: 'materialCode',
      },
      {
        name: 'materialName',
      },
      {
        name: 'inspectSumQty',
      },
      {
        name: 'uomName',
      },
      {
        name: 'supplierName',
      },
      {
        name: 'creationDate',
      },
      {
        header: intl.get('tarzan.common.label.action').d('操作'),
        width: 120,
        lock: ColumnLock.right,
        renderer: ({ record }) => {
          return (
            <PermissionButton
              type="text"
              permissionList={[
                {
                  code: `list.button.inspectMaterialDetail`,
                  type: 'button',
                  meaning: '列表页-报检条码明细',
                },
              ]}
              onClick={() => handleInspectMaterialDetail(record)}
            >
              {intl.get(`${modelPrompt}.button.inspectMaterialDetail`).d('报检条码明细')}
            </PermissionButton>
          );
        },
      },
    ];

    // 报检条码明细
    const inspectMaterialColumns: ColumnProps[] = [
      {
        name: 'objectType',
      },
      {
        name: 'objectCode',
      },
      {
        name: 'materialName',
      },
      {
        name: 'revisionCode',
      },
      {
        name: 'quantity',
      },
      {
        name: 'uomName',
      },
      {
        name: 'locatorName',
      },
      {
        name: 'supplierLot',
      },
      {
        name: 'lot',
      },
    ];

    // 开始检验
    const handleItemStartInspect = async (flag, data) => {
      if (!flag) {
        const arr = pendingSelectList.filter((record: any) => record.get('inspectDocLineStatus') != 'RELEASED');
        if (arr.length > 0) {
          notification.warning({ message: '请选择下达状态的数据' })
          return;
        }
        return startInspectInfo({
          params: pendingSelectList.map((record: Record) => record.get('inspectDocLineId')),
          onSuccess: async res => {
            notification.success({});
            await handleSearchDifferenceTab(tabKey);
            setPendingSelectList([]);
          },
          onFailed: () => {

          },
        });
      } else {
        return startInspectInfo({
          params: data.map((record) => record?.inspectDocLineId),
          onSuccess: async res => {
            // notification.success({ message: '开始检验' });
            await handleSearchDifferenceTab(tabKey);
            // setPendingSelectList([]);
          },
          onFailed: () => {

          },
        });
      }

    };

    // 重置录入值
    const handleItemResetValue = async () => {
      return resetTask({
        params: pendingSelectList.map((record: Record) => record.get('inspectDocLineId')),
        onSuccess: async res => {
          notification.success({});
          await handleSearchDifferenceTab(tabKey);
          setPendingSelectList([]);
        },
        onFailed: () => {
        },
      });
    };

    // 提交审核前的验证
    const onHandleValidSubmitData = (_docLines) => {
      const docLinesError: string[] = [];
      _docLines.forEach((docLineItem) => {
        const {
          inspectItemDesc,
          samplingQty,
          dataType,
          taskLineObjects,
          inspectResult,
        } = docLineItem;
        let count = 0;

        if (
          docLineItem.dataQtyDisposition === "DATA" ||
          docLineItem.dataQtyDisposition === "SAMPLE"
        ) {
          count = 1;
        } else {
          count = Number(docLineItem.dataQty || 0);
        }

        if (dataType === "CALCULATE_FORMULA") {
          if (docLineItem.requiredFlag === "Y") {
            if (
              ["RECORD_SAMPLE_VALUE", "RECORD_VALUE"].includes(
                docLineItem.resultDimension
              )
            ) {
              if (!["OK", "NG"].includes(inspectResult)) {
                docLinesError.push(`检验项目${inspectItemDesc}未判定，请检查！`);
              }
            }
          }
        } else {
          if (docLineItem.samplingType === "USER_DEFINED_SAMPLING") {
            count = docLineItem?.taskLineObjects?.length || 0;
          }

          if (docLineItem.requiredFlag === "Y") {
            if (
              ["RECORD_SAMPLE_VALUE", "RECORD_VALUE"].includes(
                docLineItem.resultDimension
              )
            ) {
              const taskLength = docLineItem.dataQtyDisposition === 'DATA' ? docLineItem.dataQty : samplingQty;
              // 检验记录值
              const taskLine = docLineItem?.dataType === "DECISION_VALUE" ? taskLineObjects || docLineItem?.taskLineActDtls : taskLineObjects;
              if ((taskLine || []).length < taskLength) {
                docLinesError.push(`检验项目${inspectItemDesc}的抽样数量为${samplingQty}请继续录入检验对象!`);
              } else if (!["OK", "NG"].includes(inspectResult)) {
                docLinesError.push(`检验项目${inspectItemDesc}未判定，请检查`);
              } else {
                let valueSum = 0;
                (taskLineObjects || []).forEach((taskLineItem) => {
                  const { taskLineActDtls } = taskLineItem;
                  taskLineActDtls.forEach((actItem) => {
                    if (actItem.inspectValue || actItem.inspectValue === 0) {
                      valueSum++;
                    }
                  });
                });
                if (valueSum < count && docLineItem?.dataType !== "DECISION_VALUE") {
                  docLinesError.push(`检验项目${inspectItemDesc}的记录值个数为${count}请继续录入检测值!"`);
                }
              }
            }
            if (["UNQUALIFIED_ITEMS"].includes(docLineItem.resultDimension)) {
              if (!["OK", "NG"].includes(inspectResult)) {
                docLinesError.push(`检验项目${inspectItemDesc}未判定，请检查！`);
              }
            }
          }
        }
      });

      if (docLinesError.length > 0) {
        notification.warning({ message: `${docLinesError[0]}` })
        return false;
      }
      return true;
    }


    // 提交
    const handleItemSubmit = () => {
      Modal.confirm({
        title: '是否提交',
        onOk: async () => {
          handleItemSubmitInspect();
        },
      });
    };

    //确定提交
    const handleItemSubmitInspect = async () => {
      const params = await onHandleRowValidSaveData(true);
      submitInspectInfo({
        params: params,
        onSuccess: async () => {
          notification.success({});
          await handleSearchDifferenceTab(tabKey);
          setPendingSelectList([]);
        },
      });
    }

    // 保存
    const handleItemSaveInspect = async (flag) => {
      const _modeType = inspectItemRowDS.getState('modeType');
      let docLines: any = [];
      // 不带对象行模式校验
      if (_modeType === ModeType.row) {
        docLines = await onHandleRowValidSaveData(flag);
      }
      // 判定选中数据是否要开始检验
      if (docLines.length > 0) {
        const arr = docLines.filter((e) => e.inspectDocLineStatus == 'RELEASED');
        if (arr.length > 0) {
          await handleItemStartInspect(true, arr);
        }
      } else {
        notification.warning({ message: '请选择有检验记录的数据进行保存提交' })
        return;
      }

      if (!flag) {
        await saveInspectInfo({
          params: docLines,
          onSuccess: async () => {
            notification.success({});
            await handleSearchDifferenceTab(tabKey);
            setPendingSelectList([]);
          },
        });
      } else {
        // 判断选中数据是否包含出参入参数组、校验项目参数必输
        const inspectDocLineIds: number[] = docLines.map((item) => item.inspectDocLineId);
        // 是否包含报错信息
        const _errorInfo = !handleIsTargetInspectItemIds(inspectDocLineIds) || !onHandleValidSubmitData(docLines);
        if (_errorInfo) {
          return;
        }
        //提交前的保存
        saveInspectInfo({
          params: docLines,
          onSuccess: async () => {
            // notification.success({});
            await handleSearchDifferenceTab(tabKey);
            handleItemSubmit();
            // setPendingSelectList([]);
          },
        });

      }

    }

    // 检验项目Tab-保存或提交校验及数据处理-不带对象行模式
    const onHandleRowValidSaveData = async (flag) => {
      let _lineList = [];
      if (pendingTableDS.selected.length > 0) {
        _lineList = pendingTableDS.selected.map((record) => record.toData());
      } else {
        _lineList = pendingTableDS.toData();
        // 如果是提交审核未选中状态下需要过滤不成一组的出参和入参数据
      }
      if (_lineList.length === 0) {
        return [];
      }

      // 处理删除录入值数据
      const _deleteDtlIds: Array<any> = [];
      // 处理行数据变更
      let _docLines: any = _lineList.map((lineItem: any) => {
        const _taskLineActDtls: Array<any> = [];
        let _samplingQty =
          lineItem.dataType === 'CALCULATE_FORMULA'
            ? Number(lineItem.formulaCount || 0)
            : Number(lineItem.samplingQtyCount || 0);

        const _addQtyCount = Number(lineItem.addQtyCount || 0);

        // 取最大的数
        let taskLineObjects = 0;
        lineItem.taskLineObjects?.forEach(taskLineObjectItem => {
          if (taskLineObjectItem.taskLineActDtls && taskLineObjectItem.taskLineActDtls.length > 0) {
            taskLineObjects += taskLineObjectItem.taskLineActDtls.length;
          } else {
            taskLineObjects++;
          }
        });

        if (taskLineObjects > _samplingQty) {
          _samplingQty = taskLineObjects;
        }

        _samplingQty += _addQtyCount;
        const _fieldName = lineItem.fieldName;
        const _dataType = lineItem.dataType;
        // dtl原数据
        const _cacheTaskLineActDtlObj = lineItem.cacheTaskLineActDtlObj || {};

        let _cacheSequence = 0;
        let _cacheMaxSeq = lineItem.cacheMaxSeq || 1;
        if (_dataType === 'CALCULATE_FORMULA' && _samplingQty === 2) {
          _samplingQty += 2;
        }
        for (let i = 0; i < _samplingQty; i++) {
          const _inspectValue = lineItem[`${_fieldName}_VALUE${i}`];
          const _inspectDocLineActDtlId = lineItem[`${_fieldName}_VALUE${i}_ID`];
          if (_inspectDocLineActDtlId) {
            const _baseInfo = _cacheTaskLineActDtlObj[_inspectDocLineActDtlId];
            if (_inspectValue !== _baseInfo.inspectValue) {
              if (_dataType === 'CALCULATE_FORMULA' && !isNil(_inspectValue)) {
                // 计算公式空数据删除
                _deleteDtlIds.push(_inspectDocLineActDtlId);
              } else {
                _taskLineActDtls.push({
                  ..._baseInfo,
                  inspectValue: _inspectValue,
                  inspectResult: _inspectValue
                    ? lineItem[`${_fieldName}_VALUE${i}_COLOR`] === 'red'
                      ? 'NG'
                      : 'OK'
                    : null,
                });
              }
            }
            _cacheSequence = _baseInfo.sequence;
          } else if (!isNil(_inspectValue)) {
            _cacheSequence++;
            _taskLineActDtls.push({
              objectEnterValueSequence: _cacheMaxSeq,
              inspectValue: _inspectValue,
              inspectResult: _inspectValue
                ? lineItem[`${_fieldName}_VALUE${i}_COLOR`] === 'red'
                  ? 'NG'
                  : 'OK'
                : null,
              sequence: _cacheSequence,
            });
            _cacheMaxSeq++;
          }
        }

        const _maxSamplingQty = Number(lineItem.maxSamplingQty || 0);
        if (_maxSamplingQty > _samplingQty) {
          // 是否是已存在的dtl
          let _existActDtlIdFlag = false;
          for (let i = _samplingQty; i < _maxSamplingQty; i++) {
            if (!_existActDtlIdFlag) {
              const _inspectDocLineActDtlId = lineItem[`${_fieldName}_VALUE${i}_ID`];
              if (_inspectDocLineActDtlId) {
                _deleteDtlIds.push(_inspectDocLineActDtlId);
              } else {
                _existActDtlIdFlag = true;
              }
            }
          }
        }

        let _cacheAddLine = {};
        if (!lineItem.inspectDocLineId && lineItem.inspectItemId) {
          _cacheAddLine = cacheAddLineItemInfo[`ADD_${lineItem.inspectItemId}`] || {};
        }
        const _lineItem = omit(lineItem, ['cacheMaxSeq', 'cacheTaskLineActDtlObj', 'maxSamplingQty']);
        return {
          ..._cacheAddLine,
          ..._lineItem,
          taskLineActDtls: _taskLineActDtls,
        };
      });

      // _docLines.inspectDocLineActDtlIds = Array.from(new Set(_deleteDtlIds));
      if (flag && pendingTableDS.selected.length === 0) {
        // 如果是保存就全部提交、反之是提交就需要校验检验记录是否有值（根据结果纬度：只判定项目结果是否合格，不记录检测值（UNQUALIFIED_ITEMS））
        _docLines = _docLines.filter((item) => {
          // 根据结果纬度：只判定项目结果是否合格，不记录检测值（UNQUALIFIED_ITEMS）
          if (
            item.resultDimension === "QUALIFIED_ITEMS" &&
            item.inspectResult
          ) {
            return true;
          }
          // 检验记录框个数
          const _taskLineCount = (item?._dataQtyValue || []).length;
          if (item.taskLineActDtls?.length === _taskLineCount) {
            return item.taskLineActDtls.every((taskLineObjectsItem) => taskLineObjectsItem?.inspectValue);
          }
          // 过滤出没有检测值的项目
          if (item.taskLineObjects?.length && item.taskLineObjects?.length === _taskLineCount) {
            return item.taskLineObjects.every(
              (taskLineObjectsItem) =>
                !!taskLineObjectsItem?.taskLineActDtls[0]?.inspectValue || taskLineObjectsItem?.inspectValue
            );
          }
          return false;
        });
        // 入参出参id汇总
        const _targetInspectItemIds = pendingTableDS.getState('targetInspectItemIds') || [];
        const filterTargetInspectItemIds = _docLines.map((item: any) => item.inspectDocLineId);

        _docLines = _docLines.filter((item: any) => {
          // 找到目标出参入参数组对象
          const findTargetItemArr = _targetInspectItemIds.find((targetArr) =>
            (targetArr || []).includes(item.inspectDocLineId)
          );
          if (!findTargetItemArr) {
            return true;
          }
          return findTargetItemArr.every((targetItem) =>
            filterTargetInspectItemIds.includes(targetItem)
          );
        });
      }
      return _docLines;
    };

    // 报检条码明细弹窗
    const handleInspectMaterialDetail = async record => {
      inspectMaterialDS.setQueryParameter('inspectTaskId', record?.get('inspectTaskId'));
      inspectMaterialDS.setQueryParameter(
        'customizeUnitCode',
        `${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_MAIN.OBJECT`,
      );
      const _inspectMaterialDrawer = Modal.open({
        ...drawerPropsC7n({
          ds: inspectMaterialDS,
        }),
        key: Modal.key(),
        title: intl.get(`${modelPrompt}.title.inspectMaterialDetail`).d('报检条码明细'),
        drawer: true,
        destroyOnClose: true,
        closable: true,
        maskClosable: true,
        style: {
          width: '70%',
        },
        className: 'hmes-style-modal',
        children: (
          <>
            {customizeTable(
              {
                code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_MAIN.OBJECT`,
              },
              <Table
                dataSet={inspectMaterialDS}
                columns={inspectMaterialColumns}
                customizedCode="jypto"
              />,
            )}
          </>
        ),
        onCancel: () => {
          _inspectMaterialDrawer?.close();
        },
        footer: [],
      });
      await inspectMaterialDS.query();
    };
    // 处理导出按钮使用的查询参数
    const getExportQueryParams = () => {
      if (!searchTableDS.queryDataSet || !searchTableDS.queryDataSet.current) {
        return {};
      }
      const queryParmas = searchTableDS.queryDataSet.current.toData();
      Object.keys(queryParmas).forEach(i => {
        if (isNil(queryParmas[i])) {
          delete queryParmas[i];
        }
      });
      return {
        ...queryParmas,
      };
    };

    return (
      <div className="hmes-style">
        <Header title={intl.get(`${modelPrompt}.title`).d('检验记录平台')}>
          {tabKey === 'processTask' && (
            <>
              <ExcelExport
                method="GET"
                exportAsync
                requestUrl={`${API_HOST}${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/inja-qms-doc-line/pending/export/ui`}
                queryParams={getExportQueryParams}
                buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
                otherButtonProps={{
                  disabled: true,
                }}
              />
              <PermissionButton
                type="c7n-pro"
                color={ButtonColor.primary}
                loading={submitInspectInfoLoading}
                onClick={() => handleItemSaveInspect(true)}
                permissionList={[
                  {
                    code: `list.button.submit.approveal`,
                    type: 'button',
                    meaning: '列表页-提交审核',
                  },
                ]}
              >
                {intl.get(`${modelPrompt}.button.submit.approveal`).d('提交审核')}
              </PermissionButton>
              <PermissionButton
                type="c7n-pro"
                loading={saveInspectInfoLoading}
                onClick={() => handleItemSaveInspect(false)}
                permissionList={[
                  {
                    code: `list.button.save`,
                    type: 'button',
                    meaning: '列表页-保存',
                  },
                ]}
              >
                {intl.get('tarzan.common.button.save').d('保存')}
              </PermissionButton>
              <PermissionButton
                type="c7n-pro"
                disabled={pendingSelectList.length < 1}
                onClick={handleItemResetValue}
                permissionList={[
                  {
                    code: `list.button.reset`,
                    type: 'button',
                    meaning: '列表页-重置录入值',
                  },
                ]}
              >
                {intl.get(`${modelPrompt}.button.resetValue`).d('重置录入值')}
              </PermissionButton>
              <PermissionButton
                type="c7n-pro"
                disabled={pendingSelectList.length < 1}
                loading={revokeTaskLoading}
                onClick={handleRevokeTask}
                permissionList={[
                  {
                    code: `list.button.revokeTask`,
                    type: 'button',
                    meaning: '列表页-撤销检验任务',
                  },
                ]}
              >
                {intl.get(`${modelPrompt}.button.revokeTask`).d('撤销检验项目')}
              </PermissionButton>
              <PermissionButton
                disabled={pendingSelectList.length < 1}
                loading={startInspectInfoLoading}
                onClick={() => handleItemStartInspect(false, [])}
                permissionList={[
                  {
                    code: `list.button.startInspect`,
                    type: 'button',
                    meaning: '列表页-开始检验',
                  },
                ]}
              >
                {intl.get(`${modelPrompt}.button.startInspect`).d('开始检验')}
              </PermissionButton>
            </>
          )}
          {tabKey === 'receiveTask' && (
            <>
              <ExcelExport
                method="GET"
                exportAsync
                requestUrl={`${API_HOST}${BASIC.TARZAN_SAMPLING}/v1/${tenantId}/inja-qms-doc-line/unclaimed/export/ui`}
                queryParams={getExportQueryParams}
                buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
                otherButtonProps={{
                  disabled: true,
                }}
              />
              <PermissionButton
                type="c7n-pro"
                color={ButtonColor.primary}
                disabled={unclaimedSelectList.length < 1}
                loading={receiveTaskLoading}
                onClick={() => handleReceiveTask()}
                permissionList={[
                  {
                    code: `list.button.receiveTask`,
                    type: 'button',
                    meaning: '列表页-领取检验任务',
                  },
                ]}
              >
                {intl.get(`${modelPrompt}.button.receiveTask`).d('领取检验任务')}
              </PermissionButton>
              <PermissionButton
                type="c7n-pro"
                disabled={unclaimedSelectList.length < 1}
                loading={receiveTaskLoading}
                onClick={() => handleDistributeTask()}
                permissionList={[
                  {
                    code: `list.button.distributeTask`,
                    type: 'button',
                    meaning: '列表页-分配检验任务',
                  },
                ]}
              >
                {intl.get(`${modelPrompt}.button.distributeTask`).d('分配检验任务')}
              </PermissionButton>
            </>
          )}
        </Header>
        <Content className={styles['inspection-platform-record']}>
          <Spin
            spinning={
              receiveTaskLoading ||
              revokeTaskLoading ||
              resetTaskLoading ||
              formulaCalculationLoading ||
              getCalculateFormulaLoading
            }
          >
            {customizeTable(
              {
                filterCode: `${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_MAIN.QUERY`,
              },
              <Table
                queryBar={TableQueryBarType.filterBar}
                queryBarProps={{
                  fuzzyQuery: false,
                }}
                dataSet={searchTableDS}
                columns={[]}
                searchCode="inspectionPlatformProject"
                className={styles['inspection-platform-search-table']}
              />,
            )}
            {customizeTabPane(
              {
                code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_MAIN.PAGE`,
              },
              <Tabs activeKey={tabKey} onChange={handleChangeActiveKey} animated={false}>
                <TabPane
                  tab={intl.get(`${modelPrompt}.tab.pendingTask`).d('待处理检验项目')}
                  countRenderer={() => (
                    <Badge count={pendingCount} showZero className={styles['tab-super-count']} />
                  )}
                  key="processTask"
                >
                  {customizeTable(
                    {
                      code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_MAIN.TABLE`,
                    },
                    <Table
                      queryBar={TableQueryBarType.none}
                      queryBarProps={{
                        fuzzyQuery: false,
                      }}
                      dataSet={pendingTableDS}
                      columns={columns}
                      customizedCode="jyptli"
                    />,
                  )}
                </TabPane>
                <TabPane
                  tab={intl.get(`${modelPrompt}.tab.unclaimedTask`).d('待领取检验项目')}
                  countRenderer={() => (
                    <Badge
                      count={unclaimedCount}
                      showZero
                      className={styles['tab-super-count']}
                      style={{ backgroundColor: '#11D954' }}
                    />
                  )}
                  key="receiveTask"
                >
                  {customizeTable(
                    {
                      code: `${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_MAIN.TABLE`,
                    },
                    <Table
                      queryBar={TableQueryBarType.none}
                      dataSet={unclaimedTableDs}
                      columns={unclaimedColumns}
                      customizedCode="jyptli"
                    />,
                  )}
                </TabPane>
              </Tabs>,
            )}
          </Spin>
        </Content>
      </div>
    );
  };

export default formatterCollections({
  code: ['tarzan.qms.inspectionPlatformProject', 'tarzan.common'],
})(
  withProps(
    () => {
      const searchTableDS = new DataSet({
        ...TableDS(),
      });
      // 待处理检验任务
      const pendingTableDS = new DataSet({
        ...TableDS(),
      });
      // 检验项行模式
      const inspectItemRowObjValueDS = new DataSet({
        ...InspectItemRowObjValueDS(),
      });
      // 检验项列模式
      const inspectItemColDS = new DataSet({
        ...InspectItemColDS(),
      });
      const inspectItemRowDS = new DataSet({
        ...TableDS(),
        children: {
          taskLineObjects: inspectItemRowObjValueDS,
        },
      });
      // 待领取检验任务
      const unclaimedTableDs = new DataSet({
        ...unclaimedTableDS(),
      });
      return {
        searchTableDS,
        pendingTableDS,
        unclaimedTableDs,
        inspectItemColDS,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(
    withCustomize({
      unitCode: [
        // `${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_MAIN.QUERY`,
        // `${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_MAIN.TABLE`,
        // `${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_MAIN.OBJECT`,
        // `${BASIC.CUSZ_CODE_BEFORE}.INSPECTION_TASK_MAIN.PAGE`,
      ],
      // @ts-ignore
    })(inspectionPlatformProjectList),
  ),
);
